"""
百度翻译API封装模块
提供中文到英文的翻译功能
"""

import os
import requests
import random
import json
from hashlib import md5
from typing import Optional, Dict, Any
import dotenv
from utils.logger import logger

# 加载环境变量
dotenv.load_dotenv()


class BaiduTranslator:
    """百度翻译API客户端"""

    def __init__(self):
        """初始化翻译客户端"""
        self.appid = os.getenv('BAIDU_APP_ID')
        self.appkey = os.getenv('BAIDU_APP_KEY')

        # 处理环境变量中的拼写错误
        endpoint = os.getenv('BAIDU_ENDPOINT')
        translate_path = os.getenv('BAIDU_TRANSLATE_PATH')

        if not all([self.appid, self.appkey, endpoint, translate_path]):
            raise ValueError("Missing required Baidu API configuration in .env file")

        self.url = endpoint + translate_path
        logger.info("BaiduTranslator initialized successfully")

    def _make_md5(self, s: str, encoding: str = 'utf-8') -> str:
        """生成MD5签名"""
        return md5(s.encode(encoding)).hexdigest()

    def translate(self, text: str, from_lang: str = 'zh', to_lang: str = 'en') -> Optional[str]:
        """
        翻译文本

        Args:
            text: 要翻译的文本
            from_lang: 源语言代码，默认为'zh'（中文）
            to_lang: 目标语言代码，默认为'en'（英文）

        Returns:
            翻译后的文本，如果翻译失败则返回None
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for translation")
            return None

        try:
            # 生成随机盐值和签名
            salt = random.randint(32768, 65536)
            sign = self._make_md5(self.appid + text + str(salt) + self.appkey)

            # 构建请求参数
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            payload = {
                'appid': self.appid,
                'q': text,
                'from': from_lang,
                'to': to_lang,
                'salt': salt,
                'sign': sign
            }

            # 发送请求
            logger.debug(f"Translating text: {text[:50]}...")
            response = requests.post(self.url, params=payload, headers=headers, timeout=10)
            response.raise_for_status()

            result = response.json()

            # 检查API响应
            if 'error_code' in result:
                logger.error(f"Baidu API error: {result.get('error_msg', 'Unknown error')}")
                return None

            # 提取翻译结果
            if 'trans_result' in result and result['trans_result']:
                translated_text = result['trans_result'][0]['dst']
                logger.debug(f"Translation successful: {translated_text[:50]}...")
                return translated_text
            else:
                logger.error("No translation result in API response")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error during translation: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during translation: {e}")
            return None


# 全局翻译器实例
_translator = None


def get_translator() -> BaiduTranslator:
    """获取翻译器实例（单例模式）"""
    global _translator
    if _translator is None:
        _translator = BaiduTranslator()
    return _translator


def translate_chinese_to_english(text: str) -> Optional[str]:
    """
    将中文翻译为英文的便捷函数

    Args:
        text: 要翻译的中文文本

    Returns:
        翻译后的英文文本，如果翻译失败则返回None
    """
    translator = get_translator()
    return translator.translate(text, from_lang='zh', to_lang='en')


def translate_text(text: str, from_lang: str = 'zh', to_lang: str = 'en') -> Optional[str]:
    """
    通用翻译函数

    Args:
        text: 要翻译的文本
        from_lang: 源语言代码，默认为'zh'（中文）
        to_lang: 目标语言代码，默认为'en'（英文）

    Returns:
        翻译后的文本，如果翻译失败则返回None
    """
    translator = get_translator()
    return translator.translate(text, from_lang=from_lang, to_lang=to_lang)


# 示例用法
if __name__ == "__main__":
    # 测试翻译功能
    test_text = "你好，世界！这是一个测试。"
    result = translate_chinese_to_english(test_text)

    if result:
        print(f"原文: {test_text}")
        print(f"译文: {result}")
    else:
        print("翻译失败")
