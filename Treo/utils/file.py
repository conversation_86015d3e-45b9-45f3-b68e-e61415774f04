import os
from typing import List, Dict


DEFAULT_IGNORES = ['.mvn', '.idea', '.vscode', '.augment', '.github', '.git', '.gitignore', '.claude', '.gitee', '.image', '.cursor', '.devcontainer', '.buildkit', 'qa_understand.jsonl', 'qa_document.jsonl', 'qa_develop.jsonl', 'qa_optimize.jsonl', 'qa_test.jsonl', 'node_modules', '__pycache__', 'target', 'build']
READABLE_FILE_TYPES = ['.py', '.java', '.go', '.js', '.ts', '.c', '.cpp', '.h', '.hpp', '.cs', '.rb', '.php', '.swift', '.kt', '.kts', '.rs', '.sh', '.md', '.txt', '.yaml', '.xml']


class FileUtils:
    @staticmethod
    def read_repo_dir(project_dir: str, ignores: List[str] = DEFAULT_IGNORES) -> Dict[str, str]:
        """
        递归读取project_dir中的文件，忽略ignores中的目录或文件
        在读取文件时，在每行的文件前加上行号信息，如 l:{line num} | {code}
        返回文件路径到内容的映射
        """
        doc_content = {}

        for root, dirs, files in os.walk(project_dir):
            # 过滤忽略的目录
            dirs[:] = [d for d in dirs if d not in ignores]

            for file in files:
                # 跳过忽略的文件
                if file in ignores:
                    continue
                # 跳过非可读文件
                if not file.endswith(tuple(READABLE_FILE_TYPES)):
                    continue

                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, project_dir)

                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        # 添加行号信息
                        numbered_lines = [f"l:{i+1} | {line.rstrip()}" for i, line in enumerate(lines)]
                        doc_content[relative_path] = '\n'.join(numbered_lines)
                except Exception as e:
                    print(f"Error reading file {file_path}: {e}")
                    continue

        return doc_content