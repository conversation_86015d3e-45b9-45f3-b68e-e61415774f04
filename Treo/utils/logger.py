import os
import logging
import time
from rich.logging import RichHandler

os.makedirs("logs", exist_ok=True)

# 创建自定义格式器
formatter = logging.Formatter(
    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 创建文件处理器
file_handler = logging.FileHandler(
    f"logs/log-{time.strftime('%Y-%m-%d-%H-%M-%S', time.localtime())}.log",
    encoding="utf-8"
)
file_handler.setFormatter(formatter)
file_handler.setLevel(logging.INFO)

# 创建控制台处理器
console_handler = RichHandler(markup=True, rich_tracebacks=True)
console_handler.setFormatter(logging.Formatter('%(message)s'))
console_handler.setLevel(logging.INFO)

# 获取项目的logger
logger = logging.getLogger("coderetrievalbenchmarks")
logger.setLevel(logging.INFO)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 防止重复日志（不传播到根logger）
logger.propagate = False

# 配置第三方库的日志级别，避免过多的SDK日志
logging.getLogger("datasets").setLevel(logging.WARNING)
logging.getLogger("transformers").setLevel(logging.WARNING)
logging.getLogger("torch").setLevel(logging.WARNING)
logging.getLogger("sentence_transformers").setLevel(logging.WARNING)
logging.getLogger("huggingface_hub").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("requests").setLevel(logging.WARNING)

# 为了兼容性，也设置根logger的基本配置（但级别设为WARNING以减少第三方库日志）
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[file_handler]
)