# Services Makefile
# 用于管理项目依赖的Docker服务

.PHONY: help start stop restart down status logs health wait clean backup restore

# 默认目标
help:
	@echo "可用的命令:"
	@echo "  start          - 启动所有服务"
	@echo "  start-minimal  - 启动最小化服务（仅向量数据库）"
	@echo "  stop           - 停止所有服务"
	@echo "  restart        - 重启所有服务"
	@echo "  down           - 停止并删除所有服务"
	@echo "  clean          - 停止并删除所有服务和数据卷"
	@echo "  status         - 查看服务状态"
	@echo "  logs           - 查看服务日志"
	@echo "  health         - 检查服务健康状态"
	@echo "  wait           - 等待服务启动完成"
	@echo "  backup         - 备份数据"
	@echo "  restore        - 恢复数据"
	@echo ""
	@echo "特定服务操作:"
	@echo "  qdrant-start   - 启动 Qdrant 服务"
	@echo "  qdrant-stop    - 停止 Qdrant 服务"
	@echo "  qdrant-logs    - 查看 Qdrant 日志"
	@echo "  milvus-start   - 启动 Milvus 服务"
	@echo "  milvus-stop    - 停止 Milvus 服务"
	@echo "  milvus-logs    - 查看 Milvus 日志"

# 启动所有服务
start:
	@echo "启动所有服务..."
	docker-compose up -d
	@python manage_services.py wait

# 启动最小化服务
start-minimal:
	@echo "启动最小化服务..."
	docker-compose -f docker-compose.minimal.yml up -d
	@python manage_services.py wait --minimal

# 停止所有服务
stop:
	@echo "停止所有服务..."
	docker-compose stop

# 重启所有服务
restart:
	@echo "重启所有服务..."
	docker-compose restart
	@python manage_services.py wait

# 停止并删除服务
down:
	@echo "停止并删除服务..."
	docker-compose down

# 清理所有数据
clean:
	@echo "停止并删除服务和数据卷..."
	docker-compose down -v
	@echo "清理完成"

# 查看服务状态
status:
	@echo "服务状态:"
	docker-compose ps

# 查看服务日志
logs:
	docker-compose logs

# 检查服务健康状态
health:
	@python health_check.py

# 等待服务启动完成
wait:
	@python health_check.py --wait 120

# Qdrant 相关操作
qdrant-start:
	@echo "启动 Qdrant 服务..."
	docker-compose up -d qdrant

qdrant-stop:
	@echo "停止 Qdrant 服务..."
	docker-compose stop qdrant

qdrant-logs:
	docker-compose logs qdrant

qdrant-shell:
	docker exec -it qdrant_benchmark /bin/bash

# Milvus 相关操作
milvus-start:
	@echo "启动 Milvus 服务..."
	docker-compose up -d milvus

milvus-stop:
	@echo "停止 Milvus 服务..."
	docker-compose stop milvus

milvus-logs:
	docker-compose logs milvus

milvus-shell:
	docker exec -it milvus_benchmark /bin/bash

# 数据备份
backup:
	@echo "备份数据..."
	@mkdir -p backups
	@echo "备份 Qdrant 数据..."
	docker run --rm -v services_qdrant_data:/data -v $(PWD)/backups:/backup alpine tar czf /backup/qdrant_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz -C /data .
	@echo "备份 Milvus 数据..."
	docker run --rm -v services_milvus_data:/data -v $(PWD)/backups:/backup alpine tar czf /backup/milvus_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz -C /data .
	@echo "备份完成，文件保存在 backups/ 目录"

# 数据恢复（需要指定备份文件）
restore:
	@echo "数据恢复需要手动指定备份文件"
	@echo "示例："
	@echo "  make restore-qdrant BACKUP=backups/qdrant_backup_20240101_120000.tar.gz"
	@echo "  make restore-milvus BACKUP=backups/milvus_backup_20240101_120000.tar.gz"

restore-qdrant:
	@if [ -z "$(BACKUP)" ]; then echo "请指定备份文件: make restore-qdrant BACKUP=path/to/backup.tar.gz"; exit 1; fi
	@echo "恢复 Qdrant 数据从 $(BACKUP)..."
	docker run --rm -v services_qdrant_data:/data -v $(PWD):/backup alpine tar xzf /backup/$(BACKUP) -C /data
	@echo "Qdrant 数据恢复完成"

restore-milvus:
	@if [ -z "$(BACKUP)" ]; then echo "请指定备份文件: make restore-milvus BACKUP=path/to/backup.tar.gz"; exit 1; fi
	@echo "恢复 Milvus 数据从 $(BACKUP)..."
	docker run --rm -v services_milvus_data:/data -v $(PWD):/backup alpine tar xzf /backup/$(BACKUP) -C /data
	@echo "Milvus 数据恢复完成"

# 开发相关
dev-setup: start-minimal health
	@echo "开发环境设置完成"

# 生产环境部署
prod-deploy: start wait health
	@echo "生产环境部署完成"

# 清理 Docker 系统
docker-clean:
	@echo "清理 Docker 系统..."
	docker system prune -f
	docker volume prune -f

# 更新服务镜像
update:
	@echo "更新服务镜像..."
	docker-compose pull
	docker-compose up -d --force-recreate

# 监控服务资源使用
monitor:
	@echo "监控服务资源使用情况..."
	docker stats $(shell docker-compose ps -q)

# 导出服务配置
export-config:
	@echo "导出服务配置..."
	docker-compose config > docker-compose.resolved.yml
	@echo "配置已导出到 docker-compose.resolved.yml"
