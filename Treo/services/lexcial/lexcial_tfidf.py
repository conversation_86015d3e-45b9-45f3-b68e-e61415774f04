import os
import json
import math
from typing import Dict
from collections import Counter
from services.service import IService
from utils.file import FileUtils
from utils.term import TermUtils

class LexicalTFIDF(IService):
  def __init__(self, config: Dict = {}):
    super().__init__(config)

    self.doc_content = None
    self.term_idf = None
    self.doc_term_freqs = None    

  def load_context_files(self, project_dir: str, cache_dir: str, project_id: str):
    # 读取cache_dir中，文件名为project_id.json的缓存文件
    cache_file = os.path.join(cache_dir, f"{project_id}.json")

    # 如果缓存文件存在，直接加载
    if os.path.exists(cache_file):
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
            self.doc_content = cache_data['doc_content']
            self.term_idf = cache_data['term_idf']
            self.doc_term_freqs = cache_data['doc_term_freqs']
            return {"status": "loaded_from_cache", "documents": len(self.doc_content)}

    # 确保缓存目录存在
    os.makedirs(cache_dir, exist_ok=True)

    # 若无缓存文件，则执行以下逻辑
    # 使用FileUtils读取project_dir中的文件
    self.doc_content = FileUtils.read_repo_dir(project_dir)

    # 一遍文件遍历，计算每个term的idf值， 计算每个文件中每个term的频率
    all_terms = set()
    doc_terms = {}
    doc_freqs = {}  # 记录每个term出现在多少个文档中

    # 遍历所有文档，提取terms并统计
    for file_path, content in self.doc_content.items():
        terms = TermUtils.get_terms(content)
        doc_terms[file_path] = terms
        all_terms.update(terms)

        # 统计每个term出现在多少个文档中（用于计算IDF）
        unique_terms_in_doc = set(terms)
        for term in unique_terms_in_doc:
            doc_freqs[term] = doc_freqs.get(term, 0) + 1

    # 计算每个term的IDF值
    total_docs = len(self.doc_content)
    self.term_idf = {}
    for term in all_terms:
        doc_freq = doc_freqs.get(term, 0)
        # IDF计算：log(N/df)，其中N是总文档数，df是包含该term的文档数
        self.term_idf[term] = math.log(total_docs / doc_freq) if doc_freq > 0 else 0

    # 计算每个文件中每个term的频率（TF）
    self.doc_term_freqs = {}
    for file_path, terms in doc_terms.items():
        term_counts = Counter(terms)
        self.doc_term_freqs[file_path] = dict(term_counts)

    # 存储在cache_dir中，文件名为project_id.json
    cache_data = {
        'doc_content': self.doc_content,
        'term_idf': self.term_idf,
        'doc_term_freqs': self.doc_term_freqs,
    }

    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(cache_data, f, ensure_ascii=False, indent=2)

    return {
        "status": "indexed",
        "documents": len(self.doc_content),
        "unique_terms": len(self.term_idf)
    }

  def retrieve(self, project_id: str, query: str, top_k: int) -> Dict[str, float]:
    # 检查是否已加载数据
    if not self.doc_content or not self.term_idf or not self.doc_term_freqs:
        return {}
    
    # 提取查询词
    query_terms = TermUtils.get_terms(query)
    if not query_terms:
        return {}

    query_term_tfidf = {}
    query_term_counter = Counter(query_terms)
    for term in query_terms:
        query_term_tfidf[term] = query_term_counter.get(term, 0) * self.term_idf.get(term, 0)

    # 遍历当前project_id对应的每个doc_term_freqs
    scores = {}
    for file_path, term_freqs in self.doc_term_freqs.items():

        # 若当前doc中存在任何一个query_terms的key
        has_query_term = any(term in term_freqs for term in query_terms)
        if has_query_term:
            doc_score = 0.0
            
            for term, query_tfidf in query_term_tfidf.items():
                if term in term_freqs:
                    # 计算余弦相似度
                    doc_score += query_tfidf * term_freqs[term] * self.term_idf.get(term, 0)

            if doc_score > 0:
                scores[file_path] = doc_score

    # 按分数排序并返回top_k的doc结果
    sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
    return dict(sorted_scores[:top_k])