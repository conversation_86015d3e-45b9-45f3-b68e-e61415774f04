#!/bin/bash

# Services Setup Script
# 用于快速设置和启动项目依赖服务

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装，请先安装 $1"
        return 1
    fi
    return 0
}

# 检查 Docker 和 Docker Compose
check_docker() {
    print_info "检查 Docker 环境..."
    
    if ! check_command docker; then
        print_error "请先安装 Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! check_command docker-compose; then
        print_error "请先安装 Docker Compose: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    print_success "Docker 环境检查通过"
}

# 检查 Python 环境
check_python() {
    print_info "检查 Python 环境..."
    
    if ! check_command python3; then
        print_error "请先安装 Python 3"
        exit 1
    fi
    
    if ! check_command pip3; then
        print_error "请先安装 pip3"
        exit 1
    fi
    
    print_success "Python 环境检查通过"
}

# 安装 Python 依赖
install_python_deps() {
    print_info "安装 Python 依赖库..."
    
    # 核心依赖
    pip3 install --user qdrant-client pymilvus requests python-dotenv
    
    # 可选依赖（忽略安装失败）
    pip3 install --user redis psycopg2-binary || print_warning "可选依赖安装失败，部分功能可能不可用"
    
    print_success "Python 依赖安装完成"
}

# 创建环境配置文件
setup_env() {
    print_info "设置环境配置..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "已创建 .env 文件，请根据需要修改配置"
        else
            print_warning ".env.example 文件不存在，跳过环境配置"
        fi
    else
        print_info ".env 文件已存在，跳过创建"
    fi
}

# 启动服务
start_services() {
    local mode=$1
    
    if [ "$mode" = "minimal" ]; then
        print_info "启动最小化服务（仅向量数据库）..."
        docker-compose -f docker-compose.minimal.yml up -d
    else
        print_info "启动所有服务..."
        docker-compose up -d
    fi
    
    print_info "等待服务启动完成..."
    sleep 10
    
    # 检查服务健康状态
    if python3 health_check.py --wait 120; then
        print_success "所有服务启动成功！"
    else
        print_warning "部分服务可能未正常启动，请检查日志"
    fi
}

# 运行测试
run_tests() {
    print_info "运行服务功能测试..."
    
    if python3 test_services.py; then
        print_success "所有服务功能测试通过！"
    else
        print_warning "部分服务测试失败，请检查服务状态"
    fi
}

# 显示服务信息
show_services_info() {
    print_info "服务访问信息："
    echo ""
    echo "  Qdrant:"
    echo "    - REST API: http://localhost:6333"
    echo "    - Web UI: http://localhost:6333/dashboard"
    echo ""
    echo "  Milvus:"
    echo "    - gRPC API: localhost:19530"
    echo "    - Metrics: http://localhost:9091"
    echo ""
    echo "  Redis (可选):"
    echo "    - 端口: 6379"
    echo ""
    echo "  PostgreSQL (可选):"
    echo "    - 端口: 5432"
    echo "    - 数据库: benchmark_db"
    echo "    - 用户: benchmark_user"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "Services Setup Script"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --minimal          只启动最小化服务（Qdrant + Milvus）"
    echo "  --no-deps          跳过 Python 依赖安装"
    echo "  --no-test          跳过功能测试"
    echo "  --stop             停止所有服务"
    echo "  --clean            停止并清理所有服务和数据"
    echo "  --status           显示服务状态"
    echo "  --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                 # 完整安装和启动"
    echo "  $0 --minimal       # 最小化安装"
    echo "  $0 --stop          # 停止服务"
    echo "  $0 --clean         # 清理所有数据"
    echo ""
}

# 主函数
main() {
    local minimal=false
    local no_deps=false
    local no_test=false
    local stop=false
    local clean=false
    local status=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --minimal)
                minimal=true
                shift
                ;;
            --no-deps)
                no_deps=true
                shift
                ;;
            --no-test)
                no_test=true
                shift
                ;;
            --stop)
                stop=true
                shift
                ;;
            --clean)
                clean=true
                shift
                ;;
            --status)
                status=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 切换到脚本目录
    cd "$(dirname "$0")"
    
    if [ "$stop" = true ]; then
        print_info "停止所有服务..."
        docker-compose down
        print_success "服务已停止"
        exit 0
    fi
    
    if [ "$clean" = true ]; then
        print_info "停止并清理所有服务和数据..."
        docker-compose down -v
        print_success "清理完成"
        exit 0
    fi
    
    if [ "$status" = true ]; then
        print_info "服务状态："
        docker-compose ps
        echo ""
        python3 health_check.py
        exit 0
    fi
    
    # 正常安装流程
    print_info "开始设置项目依赖服务..."
    
    check_docker
    check_python
    
    if [ "$no_deps" != true ]; then
        install_python_deps
    fi
    
    setup_env
    
    if [ "$minimal" = true ]; then
        start_services "minimal"
    else
        start_services "full"
    fi
    
    if [ "$no_test" != true ]; then
        run_tests
    fi
    
    show_services_info
    
    print_success "设置完成！"
    print_info "使用 'python3 manage_services.py --help' 查看更多管理命令"
}

# 运行主函数
main "$@"
