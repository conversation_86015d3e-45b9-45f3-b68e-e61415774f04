# Docker Services Environment Configuration
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# Qdrant 向量数据库配置
# =============================================================================
QDRANT_API_URL=http://localhost:6333
QDRANT_DATABASE=qdrant_benchmark
QDRANT_PWD=your_qdrant_password

# Qdrant Docker 配置
QDRANT_HTTP_PORT=6333
QDRANT_GRPC_PORT=6334

# =============================================================================
# Milvus 向量数据库配置
# =============================================================================
MILVUS_API_URL=http://localhost:19530
MILVUS_DATABASE=milvus_benchmark
MILVUS_PWD=your_milvus_password

# Milvus Docker 配置
MILVUS_GRPC_PORT=19530
MILVUS_METRICS_PORT=9091

# =============================================================================
# Redis 缓存配置（可选）
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# =============================================================================
# PostgreSQL 数据库配置（可选）
# =============================================================================
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=benchmark_db
POSTGRES_USER=benchmark_user
POSTGRES_PASSWORD=benchmark_pass

# =============================================================================
# Docker Compose 配置
# =============================================================================
# 项目名称前缀
COMPOSE_PROJECT_NAME=benchmark

# 网络名称
NETWORK_NAME=benchmark_network

# =============================================================================
# 服务资源限制
# =============================================================================
# Qdrant 内存限制
QDRANT_MEMORY_LIMIT=2g

# Milvus 内存限制
MILVUS_MEMORY_LIMIT=4g

# Redis 内存限制
REDIS_MEMORY_LIMIT=512m

# PostgreSQL 内存限制
POSTGRES_MEMORY_LIMIT=1g

# =============================================================================
# 数据持久化配置
# =============================================================================
# 数据卷根目录（可选，默认使用 Docker volumes）
# DATA_ROOT=/path/to/data

# Qdrant 数据目录
# QDRANT_DATA_DIR=${DATA_ROOT}/qdrant

# Milvus 数据目录
# MILVUS_DATA_DIR=${DATA_ROOT}/milvus

# Redis 数据目录
# REDIS_DATA_DIR=${DATA_ROOT}/redis

# PostgreSQL 数据目录
# POSTGRES_DATA_DIR=${DATA_ROOT}/postgres

# =============================================================================
# 日志配置
# =============================================================================
# 日志级别
LOG_LEVEL=INFO

# 日志驱动
LOG_DRIVER=json-file

# 日志文件大小限制
LOG_MAX_SIZE=10m

# 日志文件数量限制
LOG_MAX_FILE=3

# =============================================================================
# 安全配置
# =============================================================================
# 是否启用认证
ENABLE_AUTH=false

# API 密钥（如果启用认证）
API_KEY=your_api_key

# JWT 密钥（如果启用认证）
JWT_SECRET=your_jwt_secret

# =============================================================================
# 监控配置
# =============================================================================
# 是否启用监控
ENABLE_MONITORING=false

# Prometheus 端口
PROMETHEUS_PORT=9090

# Grafana 端口
GRAFANA_PORT=3000

# =============================================================================
# 备份配置
# =============================================================================
# 备份目录
BACKUP_DIR=./backups

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 是否启用自动备份
ENABLE_AUTO_BACKUP=false

# 备份时间（cron 格式）
BACKUP_SCHEDULE=0 2 * * *
