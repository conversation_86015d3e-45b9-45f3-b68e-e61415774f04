# TreeSitter 代码解析服务

这是一个基于Tree-sitter的代码解析服务，支持Java和Python语言的代码结构分析，能够提取类、方法、变量等信息，并分析它们之间的依赖关系。

## 功能特性

- **多语言支持**: 支持Java和Python语言的解析
- **并发处理**: 支持多线程并发解析，提高处理效率
- **依赖分析**: 自动分析文件间的依赖关系
- **关系提取**: 提取类继承、方法调用等关系
- **缓存优化**: 智能缓存机制，避免重复解析
- **无状态设计**: 所有处理器方法都是静态方法，确保线程安全

## 架构设计

### 核心组件

1. **TreeSitter**: 主要的解析器类，负责协调整个解析过程
2. **IProcessor**: 抽象处理器基类，定义统一的处理接口
3. **JavaProcessor**: Java语言处理器
4. **PythonProcessor**: Python语言处理器
5. **ProcessorFactory**: 处理器工厂类，根据语言类型创建对应的处理器
6. **CodeTreeNode**: 代码树节点类型系统

### 设计模式

- **工厂模式**: ProcessorFactory根据语言类型创建对应的处理器
- **抽象工厂模式**: IProcessor定义统一的处理接口
- **策略模式**: 不同语言使用不同的处理策略

## 使用方法

### 基本用法

```python
from services.treesitter.treesitter import TreeSitter

# 创建TreeSitter实例
treesitter = TreeSitter("/path/to/your/project")

# 批量解析项目中的所有文件
results = treesitter.batch_parse(concurrency=5)

# 解析单个代码片段
snippet_result = treesitter.parse_snippet(
    file_path="test.java",
    content="public class Test { }",
    line_start=1,
    line_end=1
)
```

### 高级用法

```python
# 带忽略模式的初始化
treesitter = TreeSitter(
    workdir="/path/to/project",
    ignores=["*.tmp", "test/*", "__pycache__"]
)

# 获取解析结果的详细信息
for result in results:
    print(f"File: {result.file_path}")
    
    # 遍历子节点
    for child in result.children:
        if isinstance(child, CodeTreeNodeClass):
            print(f"  Class: {child.name}")
        elif isinstance(child, CodeTreeNodeFunc):
            print(f"  Function: {child.name}")
    
    # 查看依赖关系
    dependencies = result.get_relations(RelationType.DEPENDENCY)
    print(f"  Dependencies: {len(dependencies)}")
```

## 支持的语言特性

### Java
- 类声明 (class, interface, enum)
- 方法声明 (包括构造函数)
- 字段声明
- 包声明和导入语句
- 继承关系 (extends, implements)
- 方法重写 (@Override)

### Python
- 类定义 (class)
- 函数定义 (def)
- 导入语句 (import, from...import)
- 类继承关系
- 装饰器支持

## 性能优化

### 缓存机制
- 文件内容哈希缓存，避免重复解析未修改的文件
- 解析结果缓存，提高查询效率

### 并发处理
- 使用ThreadPoolExecutor进行并发文件解析
- 无状态设计确保线程安全

### 内存优化
- 按需加载文件内容
- 智能垃圾回收机制

## 测试

运行测试用例：

```bash
python -m pytest tests/test_treesitter.py -v
```

运行演示脚本：

```bash
python examples/demo_treesitter.py
```

## 扩展支持

### 添加新语言支持

1. 创建新的处理器类继承自IProcessor
2. 实现所有抽象方法
3. 在ProcessorFactory中注册新的处理器
4. 添加对应的语言类型到SuffixLanguage枚举

示例：

```python
class CppProcessor(IProcessor):
    @staticmethod
    def extract_tree_code_nodes(node, content, file_path=None):
        # 实现C++代码解析逻辑
        pass
    
    @staticmethod
    def get_supported_language():
        return SuffixLanguage.CPP

# 注册处理器
ProcessorFactory.register_processor(SuffixLanguage.CPP, CppProcessor)
```

## 注意事项

1. **依赖要求**: 需要安装tree-sitter和对应语言的绑定包
2. **文件编码**: 默认使用UTF-8编码读取文件
3. **内存使用**: 大型项目可能需要调整并发数以控制内存使用
4. **错误处理**: 解析失败的文件会被跳过，不会中断整个处理过程

## 故障排除

### 常见问题

1. **导入错误**: 确保安装了tree-sitter相关包
2. **解析失败**: 检查文件编码和语法正确性
3. **性能问题**: 调整并发数或增加忽略模式

### 调试模式

可以通过修改代码中的print语句来启用详细的调试输出。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。在提交代码前，请确保：

1. 所有测试用例通过
2. 代码符合项目的编码规范
3. 添加了适当的文档和注释
