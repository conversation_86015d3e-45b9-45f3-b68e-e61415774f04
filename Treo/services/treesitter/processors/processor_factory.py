from typing import Optional, Type
from services.types.language import SuffixLanguage
from services.treesitter.processors.processor import IProcessor
from services.treesitter.processors.java_processor import JavaProcessor
from services.treesitter.processors.python_processor import PythonProcessor


class ProcessorFactory:
    """
    处理器工厂类，根据语言类型返回对应的处理器实例
    使用工厂模式实现处理器的创建和管理
    """
    
    # 语言到处理器类的映射
    _PROCESSOR_MAP = {
        SuffixLanguage.JAVA: JavaProcessor,
        SuffixLanguage.PYTHON: PythonProcessor,
    }
    
    @classmethod
    def get_processor(cls, language: SuffixLanguage) -> Optional[Type[IProcessor]]:
        """
        根据语言类型获取对应的处理器类
        
        Args:
            language: 语言类型
            
        Returns:
            Optional[Type[IProcessor]]: 处理器类，如果不支持该语言则返回None
        """
        return cls._PROCESSOR_MAP.get(language)
    
    @classmethod
    def is_supported(cls, language: SuffixLanguage) -> bool:
        """
        检查是否支持指定语言
        
        Args:
            language: 语言类型
            
        Returns:
            bool: 是否支持该语言
        """
        return language in cls._PROCESSOR_MAP
    
    @classmethod
    def get_supported_languages(cls) -> list[SuffixLanguage]:
        """
        获取所有支持的语言类型
        
        Returns:
            list[SuffixLanguage]: 支持的语言类型列表
        """
        return list(cls._PROCESSOR_MAP.keys())
    
    @classmethod
    def register_processor(cls, language: SuffixLanguage, processor_class: Type[IProcessor]) -> None:
        """
        注册新的处理器类
        
        Args:
            language: 语言类型
            processor_class: 处理器类
        """
        cls._PROCESSOR_MAP[language] = processor_class
    
    @classmethod
    def unregister_processor(cls, language: SuffixLanguage) -> None:
        """
        注销处理器类
        
        Args:
            language: 语言类型
        """
        if language in cls._PROCESSOR_MAP:
            del cls._PROCESSOR_MAP[language]


def get_language_from_file_extension(file_path: str) -> SuffixLanguage:
    """
    根据文件扩展名推断语言类型
    
    Args:
        file_path: 文件路径
        
    Returns:
        SuffixLanguage: 推断的语言类型
    """
    extension = file_path.lower().split('.')[-1] if '.' in file_path else ''
    
    extension_map = {
        'java': SuffixLanguage.JAVA,
        'py': SuffixLanguage.PYTHON,
    }
    
    return extension_map.get(extension, SuffixLanguage.TEXT)
