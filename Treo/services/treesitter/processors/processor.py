from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Union
from tree_sitter import Node
from services.types.code_tree_node import CodeTreeNode, CodeTreeNodeFile, CodeTreeNodeClass, CodeTreeNodeFunc, RelationType
from services.types.language import SuffixLanguage


class IProcessor(ABC):
    """
    抽象处理器基类，定义了从Tree-sitter节点提取代码结构信息的统一接口
    所有方法都设计为静态方法，确保无状态和线程安全
    """

    @staticmethod
    @abstractmethod
    def extract_tree_code_nodes(node: Node, content: str, file_path: Optional[str] = None) -> CodeTreeNode:
        """
        从Tree-sitter节点提取代码树节点

        Args:
            node: Tree-sitter解析节点
            content: 源代码内容
            file_path: 文件路径（可选）

        Returns:
            CodeTreeNode: 提取的代码树节点
        """
        pass

    @staticmethod
    @abstractmethod
    def extract_dependencies(node: Node, content: str, workdir: str) -> List[Union[str, CodeTreeNodeFile]]:
        """
        提取文件依赖关系

        Args:
            node: Tree-sitter解析节点
            content: 源代码内容
            workdir: 工作目录路径

        Returns:
            List[Union[str, CodeTreeNodeFile]]: 依赖列表，可能是字符串路径或CodeTreeNodeFile对象
        """
        pass

    @staticmethod
    @abstractmethod
    def extract_class_relations(classes: List[CodeTreeNodeClass], content: str) -> Dict[str, List[RelationType]]:
        """
        提取类之间的关系（继承、实现等）

        Args:
            classes: 类节点列表
            content: 源代码内容

        Returns:
            Dict[str, List[RelationType]]: 类名到关系类型的映射
        """
        pass

    @staticmethod
    @abstractmethod
    def extract_method_relations(methods: List[CodeTreeNodeFunc], content: str) -> Dict[str, List[RelationType]]:
        """
        提取方法之间的关系（调用、重写等）

        Args:
            methods: 方法节点列表
            content: 源代码内容

        Returns:
            Dict[str, List[RelationType]]: 方法名到关系类型的映射
        """
        pass

    @staticmethod
    @abstractmethod
    def get_supported_language() -> SuffixLanguage:
        """
        获取处理器支持的语言类型

        Returns:
            SuffixLanguage: 支持的语言类型
        """
        pass

    @staticmethod
    def extract_code_snippet(node: Node, content: str) -> str:
        """
        从节点提取代码片段

        Args:
            node: Tree-sitter节点
            content: 源代码内容

        Returns:
            str: 代码片段
        """
        start_byte = node.start_byte
        end_byte = node.end_byte
        return content[start_byte:end_byte]

    @staticmethod
    def get_line_numbers(node: Node) -> tuple[int, int]:
        """
        获取节点的行号范围

        Args:
            node: Tree-sitter节点

        Returns:
            tuple[int, int]: (起始行号, 结束行号)，行号从1开始
        """
        return node.start_point[0] + 1, node.end_point[0] + 1