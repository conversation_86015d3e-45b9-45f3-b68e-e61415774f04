import os
import re
from typing import List, Dict, Optional, Union
from tree_sitter import Node
from services.types.code_tree_node import (
    CodeTreeNode, CodeTreeNodeFile, CodeTreeNodeClass, CodeTreeNodeFunc, RelationType
)
from services.types.language import SuffixLanguage
from services.treesitter.processors.processor import IProcessor


class PythonProcessor(IProcessor):
    """Python语言处理器，用于解析Python代码结构"""
    
    @staticmethod
    def extract_tree_code_nodes(node: Node, content: str, file_path: Optional[str] = None) -> CodeTreeNode:
        """
        从Tree-sitter节点提取Python代码树节点
        """
        line_start, line_end = PythonProcessor.get_line_numbers(node)
        code_snippet = PythonProcessor.extract_code_snippet(node, content)
        
        # 根据节点类型创建不同的CodeTreeNode
        if node.type == "module":
            # 文件级别节点
            if file_path:
                file_node = CodeTreeNodeFile(
                    code_snippet=content,
                    line_start=1,
                    line_end=len(content.split('\n')),
                    file_path=file_path
                )
                PythonProcessor._extract_children(node, content, file_node)
                return file_node
            else:
                root_node = CodeTreeNode(code_snippet, line_start, line_end)
                PythonProcessor._extract_children(node, content, root_node)
                return root_node
                
        elif node.type == "class_definition":
            class_name = PythonProcessor._extract_class_name(node)
            class_node = CodeTreeNodeClass(
                code_snippet=code_snippet,
                line_start=line_start,
                line_end=line_end,
                name=class_name
            )
            PythonProcessor._extract_children(node, content, class_node)
            return class_node
            
        elif node.type == "function_definition":
            function_name = PythonProcessor._extract_function_name(node)
            parameters = PythonProcessor._extract_function_parameters(node, content)
            return_type = PythonProcessor._extract_function_return_type(node, content)
            
            function_node = CodeTreeNodeFunc(
                code_snippet=code_snippet,
                line_start=line_start,
                line_end=line_end,
                name=function_name,
                parameters=parameters,
                outputs=return_type
            )
            PythonProcessor._extract_children(node, content, function_node)
            return function_node
            
        else:
            # 通用节点
            generic_node = CodeTreeNode(code_snippet, line_start, line_end)
            PythonProcessor._extract_children(node, content, generic_node)
            return generic_node
    
    @staticmethod
    def _extract_children(parent_node: Node, content: str, tree_node: CodeTreeNode) -> None:
        """递归提取子节点"""
        for child in parent_node.children:
            if child.type in ["class_definition", "function_definition", "decorated_definition"]:
                child_tree_node = PythonProcessor.extract_tree_code_nodes(child, content)
                tree_node.add_child(child_tree_node)
    
    @staticmethod
    def _extract_class_name(node: Node) -> str:
        """提取类名"""
        for child in node.children:
            if child.type == "identifier":
                return child.text.decode('utf-8')
        return "UnknownClass"
    
    @staticmethod
    def _extract_function_name(node: Node) -> str:
        """提取函数名"""
        for child in node.children:
            if child.type == "identifier":
                return child.text.decode('utf-8')
        return "unknown_function"
    
    @staticmethod
    def _extract_function_parameters(node: Node, content: str) -> List[tuple[str, str]]:
        """提取函数参数"""
        parameters = []
        for child in node.children:
            if child.type == "parameters":
                for param_child in child.children:
                    if param_child.type == "identifier":
                        param_name = param_child.text.decode('utf-8')
                        parameters.append((param_name, ""))
                    elif param_child.type == "typed_parameter":
                        param_name = ""
                        param_type = ""
                        for typed_child in param_child.children:
                            if typed_child.type == "identifier":
                                param_name = typed_child.text.decode('utf-8')
                            elif typed_child.type == "type":
                                param_type = typed_child.text.decode('utf-8')
                        if param_name:
                            parameters.append((param_name, param_type))
        return parameters
    
    @staticmethod
    def _extract_function_return_type(node: Node, content: str) -> Optional[str]:
        """提取函数返回类型"""
        for child in node.children:
            if child.type == "type":
                return child.text.decode('utf-8')
        return None
    
    @staticmethod
    def extract_dependencies(node: Node, content: str, workdir: str) -> List[Union[str, CodeTreeNodeFile]]:
        """
        提取Python文件的依赖关系（import语句）
        """
        dependencies = []
        
        def _extract_imports(n: Node):
            if n.type == "import_statement":
                # import module
                for child in n.children:
                    if child.type == "dotted_name":
                        import_path = child.text.decode('utf-8')
                        file_path = PythonProcessor._import_to_file_path(import_path, workdir)
                        if file_path and os.path.exists(file_path):
                            dependencies.append(file_path)
                        else:
                            dependencies.append(import_path)
            elif n.type == "import_from_statement":
                # from module import ...
                module_name = ""
                for child in n.children:
                    if child.type == "dotted_name":
                        module_name = child.text.decode('utf-8')
                        break
                
                if module_name:
                    file_path = PythonProcessor._import_to_file_path(module_name, workdir)
                    if file_path and os.path.exists(file_path):
                        dependencies.append(file_path)
                    else:
                        dependencies.append(module_name)
            
            for child in n.children:
                _extract_imports(child)
        
        _extract_imports(node)
        return dependencies
    
    @staticmethod
    def _import_to_file_path(import_path: str, workdir: str) -> Optional[str]:
        """将Python import路径转换为文件路径"""
        # 将模块路径转换为文件路径
        # 例如: package.module -> package/module.py 或 package/module/__init__.py
        file_path = import_path.replace('.', os.path.sep)
        
        possible_paths = [
            os.path.join(workdir, file_path + '.py'),
            os.path.join(workdir, file_path, '__init__.py'),
            os.path.join(workdir, 'src', file_path + '.py'),
            os.path.join(workdir, 'src', file_path, '__init__.py')
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    @staticmethod
    def extract_class_relations(classes: List[CodeTreeNodeClass], content: str) -> Dict[str, List[RelationType]]:
        """
        提取Python类之间的关系
        """
        relations = {}
        
        for class_node in classes:
            class_name = class_node.name
            relations[class_name] = []
            
            # 在类的代码片段中查找继承关系
            class_code = class_node.code_snippet
            
            # 查找继承关系 class ClassName(ParentClass):
            inherit_pattern = r'class\s+' + re.escape(class_name) + r'\s*\(\s*([^)]+)\s*\):'
            inherit_match = re.search(inherit_pattern, class_code)
            if inherit_match:
                parent_classes = [cls.strip() for cls in inherit_match.group(1).split(',')]
                for _ in parent_classes:
                    relations[class_name].append(RelationType.INHERIT)
        
        return relations
    
    @staticmethod
    def extract_method_relations(methods: List[CodeTreeNodeFunc], content: str) -> Dict[str, List[RelationType]]:
        """
        提取Python方法之间的关系
        """
        relations = {}
        
        for method_node in methods:
            method_name = method_node.name
            relations[method_name] = []
            
            # 在方法体中查找方法调用
            method_code = method_node.code_snippet
            
            # 简单的方法调用匹配
            call_pattern = r'(\w+)\s*\('
            calls = re.findall(call_pattern, method_code)
            
            for call in calls:
                if call != method_name and any(m.name == call for m in methods):
                    relations[method_name].append(RelationType.CALL)
        
        return relations
    
    @staticmethod
    def get_supported_language() -> SuffixLanguage:
        """返回支持的语言类型"""
        return SuffixLanguage.PYTHON
