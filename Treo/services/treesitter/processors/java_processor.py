import os
import re
from typing import List, Dict, Optional, Union
from tree_sitter import Node
from services.types.code_tree_node import (
    CodeTreeNode, CodeTreeNodeFile, CodeTreeNodeClass, CodeTreeNodeFunc, RelationType
)
from services.types.language import SuffixLanguage
from services.treesitter.processors.processor import IProcessor


class JavaProcessor(IProcessor):
    """Java语言处理器，用于解析Java代码结构"""

    @staticmethod
    def extract_tree_code_nodes(node: Node, content: str, file_path: Optional[str] = None) -> CodeTreeNode:
        """
        从Tree-sitter节点提取Java代码树节点
        """
        line_start, line_end = JavaProcessor.get_line_numbers(node)
        code_snippet = JavaProcessor.extract_code_snippet(node, content)

        # 根据节点类型创建不同的CodeTreeNode
        if node.type == "compilation_unit":
            # 文件级别节点
            if file_path:
                file_node = CodeTreeNodeFile(
                    code_snippet=content,
                    line_start=1,
                    line_end=len(content.split('\n')),
                    file_path=file_path
                )
                JavaProcessor._extract_children(node, content, file_node)
                return file_node
            else:
                root_node = CodeTreeNode(code_snippet, line_start, line_end)
                JavaProcessor._extract_children(node, content, root_node)
                return root_node

        elif node.type == "class_declaration":
            class_name = JavaProcessor._extract_class_name(node)
            class_node = CodeTreeNodeClass(
                code_snippet=code_snippet,
                line_start=line_start,
                line_end=line_end,
                name=class_name
            )
            JavaProcessor._extract_children(node, content, class_node)
            return class_node

        elif node.type == "method_declaration":
            method_name = JavaProcessor._extract_method_name(node)
            parameters = JavaProcessor._extract_method_parameters(node, content)
            return_type = JavaProcessor._extract_method_return_type(node, content)

            method_node = CodeTreeNodeFunc(
                code_snippet=code_snippet,
                line_start=line_start,
                line_end=line_end,
                name=method_name,
                parameters=parameters,
                outputs=return_type
            )
            JavaProcessor._extract_children(node, content, method_node)
            return method_node

        else:
            # 通用节点
            generic_node = CodeTreeNode(code_snippet, line_start, line_end)
            JavaProcessor._extract_children(node, content, generic_node)
            return generic_node

    @staticmethod
    def _extract_children(parent_node: Node, content: str, tree_node: CodeTreeNode) -> None:
        """递归提取子节点"""
        for child in parent_node.children:
            if child.type in ["class_declaration", "interface_declaration", "enum_declaration",
                            "method_declaration", "constructor_declaration", "field_declaration"]:
                child_tree_node = JavaProcessor.extract_tree_code_nodes(child, content)
                tree_node.add_child(child_tree_node)

    @staticmethod
    def _extract_class_name(node: Node) -> str:
        """提取类名"""
        for child in node.children:
            if child.type == "identifier":
                return child.text.decode('utf-8')
        return "UnknownClass"

    @staticmethod
    def _extract_method_name(node: Node) -> str:
        """提取方法名"""
        for child in node.children:
            if child.type == "identifier":
                return child.text.decode('utf-8')
        return "unknownMethod"

    @staticmethod
    def _extract_method_parameters(node: Node, content: str) -> List[tuple[str, str]]:
        """提取方法参数"""
        parameters = []
        for child in node.children:
            if child.type == "formal_parameters":
                for param_child in child.children:
                    if param_child.type == "formal_parameter":
                        param_type = ""
                        param_name = ""
                        for param_part in param_child.children:
                            if param_part.type in ["type_identifier", "integral_type", "floating_point_type", "boolean_type"]:
                                param_type = param_part.text.decode('utf-8')
                            elif param_part.type == "identifier":
                                param_name = param_part.text.decode('utf-8')
                        if param_name:
                            parameters.append((param_name, param_type))
        return parameters

    @staticmethod
    def _extract_method_return_type(node: Node, content: str) -> Optional[str]:
        """提取方法返回类型"""
        for child in node.children:
            if child.type in ["type_identifier", "integral_type", "floating_point_type", "boolean_type", "void_type"]:
                return child.text.decode('utf-8')
        return None

    @staticmethod
    def extract_dependencies(node: Node, content: str, workdir: str) -> List[Union[str, CodeTreeNodeFile]]:
        """
        提取Java文件的依赖关系（import语句）
        """
        dependencies = []

        def _extract_imports(n: Node):
            if n.type == "import_declaration":
                import_path = ""
                for child in n.children:
                    if child.type == "scoped_identifier":
                        import_path = child.text.decode('utf-8')
                        break

                if import_path:
                    # 尝试将import路径转换为文件路径
                    file_path = JavaProcessor._import_to_file_path(import_path, workdir)
                    if file_path and os.path.exists(file_path):
                        dependencies.append(file_path)
                    else:
                        dependencies.append(import_path)

            for child in n.children:
                _extract_imports(child)

        _extract_imports(node)
        return dependencies

    @staticmethod
    def _import_to_file_path(import_path: str, workdir: str) -> Optional[str]:
        """将Java import路径转换为文件路径"""
        # 将包路径转换为文件路径
        # 例如: com.example.MyClass -> com/example/MyClass.java
        file_path = import_path.replace('.', os.path.sep) + '.java'
        full_path = os.path.join(workdir, file_path)

        # 也检查src/main/java等常见目录结构
        common_paths = [
            os.path.join(workdir, 'src', 'main', 'java', file_path),
            os.path.join(workdir, 'src', file_path),
            full_path
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None

    @staticmethod
    def extract_class_relations(classes: List[CodeTreeNodeClass], content: str) -> Dict[str, List[RelationType]]:
        """
        提取Java类之间的关系
        """
        relations = {}

        # 使用正则表达式匹配继承和实现关系
        for class_node in classes:
            class_name = class_node.name
            relations[class_name] = []

            # 在类的代码片段中查找extends和implements关键字
            class_code = class_node.code_snippet

            # 查找继承关系 (extends)
            extends_match = re.search(r'class\s+' + re.escape(class_name) + r'\s+extends\s+(\w+)', class_code)
            if extends_match:
                parent_class = extends_match.group(1)
                relations[class_name].append(RelationType.INHERIT)

            # 查找实现关系 (implements)
            implements_match = re.search(r'class\s+' + re.escape(class_name) + r'\s+implements\s+([\w\s,]+)', class_code)
            if implements_match:
                interfaces = [iface.strip() for iface in implements_match.group(1).split(',')]
                for _ in interfaces:
                    relations[class_name].append(RelationType.IMPLEMENT)

        return relations

    @staticmethod
    def extract_method_relations(methods: List[CodeTreeNodeFunc], content: str) -> Dict[str, List[RelationType]]:
        """
        提取Java方法之间的关系
        """
        relations = {}

        for method_node in methods:
            method_name = method_node.name
            relations[method_name] = []

            # 在方法体中查找方法调用
            method_code = method_node.code_snippet

            # 简单的方法调用匹配（可以进一步优化）
            call_pattern = r'(\w+)\s*\('
            calls = re.findall(call_pattern, method_code)

            for call in calls:
                if call != method_name and any(m.name == call for m in methods):
                    relations[method_name].append(RelationType.CALL)

            # 检查@Override注解
            if '@Override' in method_code:
                relations[method_name].append(RelationType.OVERWRITE)

        return relations

    @staticmethod
    def get_supported_language() -> SuffixLanguage:
        """返回支持的语言类型"""
        return SuffixLanguage.JAVA