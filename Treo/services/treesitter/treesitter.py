import os
import fnmatch
import hashlib
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Set
import tree_sitter_java as tsjava
import tree_sitter_python as tspython
from tree_sitter import Language, Parser

from services.types.language import SuffixLanguage
from services.types.code_tree_node import CodeTreeNode, CodeTreeNodeFile, CodeTreeNodeClass, CodeTreeNodeFunc, RelationType
from services.treesitter.processors.processor_factory import ProcessorFactory, get_language_from_file_extension


class TreeSitter:
    """
    TreeSitter类是一个实例，内部具有多种语言的Parser实例，支持并发地对多个文件进行解析
    握有工作区路径信息，用于识别不同文件间的函数调用及依赖关系
    """

    def __init__(self, workdir: str, ignores: List[str] = None):
        """
        初始化TreeSitter实例

        Args:
            workdir: 工作目录路径
            ignores: 忽略的文件或目录模式列表
        """
        self.workdir = os.path.abspath(workdir)
        self.ignores = ignores or []

        # 初始化语言解析器
        self.parsers = self._init_parsers()

        # 缓存已解析的文件
        self.parsed_files: Dict[str, CodeTreeNodeFile] = {}

        # 文件内容哈希缓存，用于检测文件变化
        self.file_hashes: Dict[str, str] = {}

    def _init_parsers(self) -> Dict[SuffixLanguage, Parser]:
        """初始化各种语言的解析器"""
        parsers = {}

        try:
            # 初始化Java解析器
            java_language = Language(tsjava.language())
            java_parser = Parser(java_language)
            parsers[SuffixLanguage.JAVA] = java_parser
        except Exception as e:
            print(f"Failed to initialize Java parser: {e}")

        try:
            # 初始化Python解析器
            python_language = Language(tspython.language())
            python_parser = Parser(python_language)
            parsers[SuffixLanguage.PYTHON] = python_parser
        except Exception as e:
            print(f"Failed to initialize Python parser: {e}")

        return parsers

    def _load_gitignore(self) -> List[str]:
        """读取.gitignore文件中的忽略模式"""
        gitignore_path = os.path.join(self.workdir, '.gitignore')
        gitignore_patterns = []

        if os.path.exists(gitignore_path):
            try:
                with open(gitignore_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            gitignore_patterns.append(line)
            except Exception as e:
                print(f"Failed to read .gitignore: {e}")

        return gitignore_patterns

    def _should_ignore(self, file_path: str, ignore_patterns: List[str]) -> bool:
        """检查文件是否应该被忽略"""
        relative_path = os.path.relpath(file_path, self.workdir)

        for pattern in ignore_patterns:
            if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(os.path.basename(file_path), pattern):
                return True

        return False

    def _scan_files(self) -> List[str]:
        """扫描工作目录中的所有文件"""
        # 合并gitignore和用户指定的忽略模式
        all_ignores = self._load_gitignore() + self.ignores

        # 添加一些默认忽略模式
        default_ignores = [
            '.git/*', '.git/**/*',
            '*.pyc', '__pycache__/*', '__pycache__/**/*',
            '*.class', 'target/*', 'target/**/*',
            'node_modules/*', 'node_modules/**/*',
            '.idea/*', '.idea/**/*',
            '.vscode/*', '.vscode/**/*'
        ]
        all_ignores.extend(default_ignores)

        files = []
        for root, dirs, filenames in os.walk(self.workdir):
            # 过滤目录
            dirs[:] = [d for d in dirs if not self._should_ignore(os.path.join(root, d), all_ignores)]

            for filename in filenames:
                file_path = os.path.join(root, filename)
                if not self._should_ignore(file_path, all_ignores):
                    # 只处理支持的文件类型
                    language = get_language_from_file_extension(filename)
                    if ProcessorFactory.is_supported(language):
                        files.append(file_path)

        return files

    def _get_file_hash(self, file_path: str, content: str) -> str:
        """计算文件内容的哈希值"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def _is_file_changed(self, file_path: str, content: str) -> bool:
        """检查文件是否发生变化"""
        current_hash = self._get_file_hash(file_path, content)
        old_hash = self.file_hashes.get(file_path)
        return old_hash != current_hash

    def _parse_file(self, file_path: str, force_reparse: bool = False) -> Optional[CodeTreeNodeFile]:
        """
        解析单个文件（带缓存优化）

        Args:
            file_path: 文件路径
            force_reparse: 是否强制重新解析

        Returns:
            Optional[CodeTreeNodeFile]: 解析结果，如果解析失败则返回None
        """
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查缓存
            if not force_reparse and file_path in self.parsed_files:
                if not self._is_file_changed(file_path, content):
                    return self.parsed_files[file_path]

            # 更新文件哈希
            self.file_hashes[file_path] = self._get_file_hash(file_path, content)

            # 确定语言类型
            language = get_language_from_file_extension(file_path)
            if not ProcessorFactory.is_supported(language):
                return None

            # 获取对应的解析器和处理器
            parser = self.parsers.get(language)
            processor_class = ProcessorFactory.get_processor(language)

            if not parser or not processor_class:
                return None

            # 使用Tree-sitter解析
            tree = parser.parse(content.encode('utf-8'))
            root_node = tree.root_node

            # 使用处理器提取代码结构
            file_node = processor_class.extract_tree_code_nodes(root_node, content, file_path)

            if isinstance(file_node, CodeTreeNodeFile):
                result = file_node
            else:
                # 如果不是文件节点，创建一个文件节点包装
                result = CodeTreeNodeFile(
                    code_snippet=content,
                    line_start=1,
                    line_end=len(content.split('\n')),
                    file_path=file_path
                )
                result.add_child(file_node)

            # 更新缓存
            self.parsed_files[file_path] = result
            return result

        except Exception as e:
            print(f"Failed to parse file {file_path}: {e}")
            return None

    def batch_parse(self, concurrency: int = 5) -> List[CodeTreeNode]:
        """
        批量解析工作目录中的所有文件

        Args:
            concurrency: 并发数

        Returns:
            List[CodeTreeNode]: 解析结果列表，每个元素是以CodeTreeNodeFile为根节点的树
        """
        # 扫描所有文件
        files = self._scan_files()

        # 并发解析文件
        results = []
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            # 提交解析任务
            future_to_file = {executor.submit(self._parse_file, file_path): file_path
                            for file_path in files}

            # 收集结果
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                        self.parsed_files[file_path] = result
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")

        # 后处理：解析依赖关系
        self._resolve_dependencies(results)

        # 后处理：解析类和方法关系
        self._resolve_relations(results)

        return results

    def parse_snippet(self, file_path: str, content: str, line_start: int, line_end: int) -> Optional[CodeTreeNode]:
        """
        解析代码片段

        Args:
            file_path: 文件路径
            content: 代码内容
            line_start: 起始行号
            line_end: 结束行号

        Returns:
            Optional[CodeTreeNode]: 解析结果
        """
        try:
            # 确定语言类型
            language = get_language_from_file_extension(file_path)
            if not ProcessorFactory.is_supported(language):
                return None

            # 获取对应的解析器和处理器
            parser = self.parsers.get(language)
            processor_class = ProcessorFactory.get_processor(language)

            if not parser or not processor_class:
                return None

            # 使用Tree-sitter解析
            tree = parser.parse(content.encode('utf-8'))
            root_node = tree.root_node

            # 使用处理器提取代码结构
            result = processor_class.extract_tree_code_nodes(root_node, content)

            return result

        except Exception as e:
            print(f"Failed to parse snippet: {e}")
            return None

    def _resolve_dependencies(self, file_nodes: List[CodeTreeNodeFile]) -> None:
        """
        解析文件间的依赖关系

        Args:
            file_nodes: 文件节点列表
        """
        # 创建文件路径到节点的映射
        path_to_node = {node.file_path: node for node in file_nodes}

        for file_node in file_nodes:
            try:
                # 确定语言类型
                language = get_language_from_file_extension(file_node.file_path)
                processor_class = ProcessorFactory.get_processor(language)

                if not processor_class:
                    continue

                # 重新解析文件以获取依赖
                with open(file_node.file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                parser = self.parsers.get(language)
                if parser:
                    tree = parser.parse(content.encode('utf-8'))
                    dependencies = processor_class.extract_dependencies(tree.root_node, content, self.workdir)

                    # 将依赖添加到关系中
                    for dep in dependencies:
                        if isinstance(dep, str):
                            # 如果是字符串路径，尝试找到对应的文件节点
                            if dep in path_to_node:
                                file_node.add_relation(RelationType.DEPENDENCY, path_to_node[dep])
                            else:
                                file_node.add_relation(RelationType.DEPENDENCY, dep)
                        else:
                            file_node.add_relation(RelationType.DEPENDENCY, dep)

            except Exception as e:
                print(f"Failed to resolve dependencies for {file_node.file_path}: {e}")

    def _resolve_relations(self, file_nodes: List[CodeTreeNodeFile]) -> None:
        """
        解析类和方法之间的关系

        Args:
            file_nodes: 文件节点列表
        """
        # 收集所有类和方法
        all_classes = []
        all_methods = []

        def collect_nodes(node: CodeTreeNode):
            if isinstance(node, CodeTreeNodeClass):
                all_classes.append(node)
            elif isinstance(node, CodeTreeNodeFunc):
                all_methods.append(node)

            for child in node.children:
                collect_nodes(child)

        for file_node in file_nodes:
            collect_nodes(file_node)

        # 为每个文件解析类和方法关系
        for file_node in file_nodes:
            try:
                language = get_language_from_file_extension(file_node.file_path)
                processor_class = ProcessorFactory.get_processor(language)

                if not processor_class:
                    continue

                with open(file_node.file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 获取该文件中的类和方法
                file_classes = []
                file_methods = []

                def collect_file_nodes(node: CodeTreeNode):
                    if isinstance(node, CodeTreeNodeClass):
                        file_classes.append(node)
                    elif isinstance(node, CodeTreeNodeFunc):
                        file_methods.append(node)

                    for child in node.children:
                        collect_file_nodes(child)

                collect_file_nodes(file_node)

                # 解析类关系
                if file_classes:
                    class_relations = processor_class.extract_class_relations(file_classes, content)
                    for class_name, relations in class_relations.items():
                        class_node = next((c for c in file_classes if c.name == class_name), None)
                        if class_node:
                            for relation_type in relations:
                                # 这里可以进一步实现具体的关系目标查找
                                pass

                # 解析方法关系
                if file_methods:
                    method_relations = processor_class.extract_method_relations(file_methods, content)
                    for method_name, relations in method_relations.items():
                        method_node = next((m for m in file_methods if m.name == method_name), None)
                        if method_node:
                            for relation_type in relations:
                                # 这里可以进一步实现具体的关系目标查找
                                pass

            except Exception as e:
                print(f"Failed to resolve relations for {file_node.file_path}: {e}")
