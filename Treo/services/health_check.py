#!/usr/bin/env python3
"""
服务健康检查脚本
检查所有依赖服务的健康状态
"""

import os
import sys
import requests
import time
from typing import Dict, Tuple, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.settings.settings import settings

class HealthChecker:
    """服务健康检查器"""
    
    def __init__(self):
        self.services = {
            "Qdrant": {
                "url": "http://localhost:6333/health",
                "timeout": 5,
                "expected_status": 200
            },
            "Milvus": {
                "url": "http://localhost:9091/healthz",
                "timeout": 10,
                "expected_status": 200
            },
            "Redis": {
                "url": "http://localhost:6379",
                "timeout": 5,
                "check_method": "redis"
            },
            "PostgreSQL": {
                "url": "postgresql://benchmark_user:benchmark_pass@localhost:5432/benchmark_db",
                "timeout": 5,
                "check_method": "postgres"
            }
        }
    
    def check_http_service(self, name: str, config: Dict) -> Tuple[bool, str]:
        """检查HTTP服务健康状态"""
        try:
            response = requests.get(
                config["url"], 
                timeout=config["timeout"]
            )
            
            expected_status = config.get("expected_status", 200)
            if response.status_code == expected_status:
                return True, f"✓ {name} 服务正常 (状态码: {response.status_code})"
            else:
                return False, f"✗ {name} 服务异常 (状态码: {response.status_code})"
                
        except requests.exceptions.ConnectionError:
            return False, f"✗ {name} 服务连接失败 (服务可能未启动)"
        except requests.exceptions.Timeout:
            return False, f"✗ {name} 服务响应超时"
        except Exception as e:
            return False, f"✗ {name} 服务检查失败: {str(e)}"
    
    def check_redis_service(self, name: str, config: Dict) -> Tuple[bool, str]:
        """检查Redis服务健康状态"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, decode_responses=True)
            r.ping()
            return True, f"✓ {name} 服务正常"
        except ImportError:
            return False, f"✗ {name} 检查失败: 缺少 redis 库 (pip install redis)"
        except redis.exceptions.ConnectionError:
            return False, f"✗ {name} 服务连接失败 (服务可能未启动)"
        except Exception as e:
            return False, f"✗ {name} 服务检查失败: {str(e)}"
    
    def check_postgres_service(self, name: str, config: Dict) -> Tuple[bool, str]:
        """检查PostgreSQL服务健康状态"""
        try:
            import psycopg2
            conn = psycopg2.connect(
                host="localhost",
                port=5432,
                database="benchmark_db",
                user="benchmark_user",
                password="benchmark_pass",
                connect_timeout=config["timeout"]
            )
            conn.close()
            return True, f"✓ {name} 服务正常"
        except ImportError:
            return False, f"✗ {name} 检查失败: 缺少 psycopg2 库 (pip install psycopg2-binary)"
        except Exception as e:
            return False, f"✗ {name} 服务连接失败: {str(e)}"
    
    def check_service(self, name: str, config: Dict) -> Tuple[bool, str]:
        """检查单个服务健康状态"""
        check_method = config.get("check_method", "http")
        
        if check_method == "redis":
            return self.check_redis_service(name, config)
        elif check_method == "postgres":
            return self.check_postgres_service(name, config)
        else:
            return self.check_http_service(name, config)
    
    def check_all_services(self, include_optional: bool = False) -> Dict[str, Tuple[bool, str]]:
        """检查所有服务健康状态"""
        results = {}
        
        # 核心服务（必需）
        core_services = ["Qdrant", "Milvus"]
        
        # 可选服务
        optional_services = ["Redis", "PostgreSQL"]
        
        services_to_check = core_services
        if include_optional:
            services_to_check.extend(optional_services)
        
        for service_name in services_to_check:
            if service_name in self.services:
                results[service_name] = self.check_service(service_name, self.services[service_name])
        
        return results
    
    def wait_for_services(self, timeout: int = 120, include_optional: bool = False) -> bool:
        """等待服务启动完成"""
        print(f"等待服务启动完成（超时: {timeout}秒）...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            results = self.check_all_services(include_optional)
            
            all_healthy = all(result[0] for result in results.values())
            if all_healthy:
                print("\n✓ 所有服务已启动并健康")
                return True
            
            print(".", end="", flush=True)
            time.sleep(5)
        
        print(f"\n✗ 服务启动超时（{timeout}秒）")
        return False
    
    def print_service_status(self, include_optional: bool = False):
        """打印服务状态"""
        print("=== 服务健康检查 ===\n")
        
        results = self.check_all_services(include_optional)
        
        healthy_count = 0
        total_count = len(results)
        
        for service_name, (is_healthy, message) in results.items():
            print(message)
            if is_healthy:
                healthy_count += 1
        
        print(f"\n总结: {healthy_count}/{total_count} 个服务正常运行")
        
        if healthy_count == total_count:
            print("🎉 所有服务运行正常！")
            return True
        else:
            print("⚠️  部分服务异常，请检查服务状态")
            return False
    
    def check_settings_compatibility(self):
        """检查设置与服务的兼容性"""
        print("\n=== 检查配置兼容性 ===\n")
        
        # 检查 Qdrant 配置
        try:
            settings.validate_qdrant_settings()
            expected_url = "http://localhost:6333"
            if settings.qdrant_api_url == expected_url:
                print(f"✓ Qdrant 配置正确: {settings.qdrant_api_url}")
            else:
                print(f"⚠️  Qdrant URL 不匹配: 配置={settings.qdrant_api_url}, 期望={expected_url}")
        except ValueError as e:
            print(f"✗ Qdrant 配置错误: {e}")
        
        # 检查 Milvus 配置
        try:
            settings.validate_milvus_settings()
            expected_url = "http://localhost:19530"
            if settings.milvus_api_url == expected_url:
                print(f"✓ Milvus 配置正确: {settings.milvus_api_url}")
            else:
                print(f"⚠️  Milvus URL 不匹配: 配置={settings.milvus_api_url}, 期望={expected_url}")
        except ValueError as e:
            print(f"✗ Milvus 配置错误: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="检查服务健康状态")
    parser.add_argument("--include-optional", action="store_true", 
                       help="包括可选服务（Redis, PostgreSQL）")
    parser.add_argument("--wait", type=int, metavar="SECONDS",
                       help="等待服务启动完成的超时时间（秒）")
    parser.add_argument("--check-config", action="store_true",
                       help="检查配置兼容性")
    
    args = parser.parse_args()
    
    checker = HealthChecker()
    
    try:
        if args.wait:
            success = checker.wait_for_services(args.wait, args.include_optional)
            if not success:
                sys.exit(1)
        
        success = checker.print_service_status(args.include_optional)
        
        if args.check_config:
            checker.check_settings_compatibility()
        
        if not success:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n检查已取消")
        sys.exit(1)
    except Exception as e:
        print(f"检查失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
