# Services Docker 部署

这个目录包含了项目依赖服务的 Docker Compose 配置文件，用于快速启动和管理向量数据库等依赖服务。

## 📋 支持的服务

### 核心服务
- **Qdrant** - 向量数据库 (端口: 6333, 6334)
- **Milvus** - 向量数据库 (端口: 19530, 9091)

### 可选服务
- **Redis** - 缓存服务 (端口: 6379)
- **PostgreSQL** - 关系数据库 (端口: 5432)

## 🚀 快速开始

### 1. 启动所有服务

```bash
# 使用完整配置启动所有服务
cd services
docker-compose up -d

# 或者使用管理脚本
python manage_services.py start
```

### 2. 启动最小化服务（推荐）

```bash
# 只启动核心的向量数据库服务
docker-compose -f docker-compose.minimal.yml up -d

# 或者使用管理脚本
python manage_services.py start --minimal
```

### 3. 检查服务状态

```bash
# 查看服务状态
docker-compose ps

# 或者使用管理脚本
python manage_services.py status
```

## 🛠️ 管理脚本使用

项目提供了 `manage_services.py` 脚本来简化服务管理：

### 基本命令

```bash
# 启动服务
python manage_services.py start

# 启动最小化服务
python manage_services.py start --minimal

# 停止服务
python manage_services.py stop

# 重启服务
python manage_services.py restart

# 停止并删除服务
python manage_services.py down

# 停止并删除服务（包括数据卷）
python manage_services.py down --remove-volumes

# 查看服务状态
python manage_services.py status

# 查看服务日志
python manage_services.py logs

# 跟踪服务日志
python manage_services.py logs --follow

# 等待服务启动完成
python manage_services.py wait
```

### 指定特定服务

```bash
# 只启动 Qdrant
python manage_services.py start --services qdrant

# 只重启 Milvus
python manage_services.py restart --services milvus

# 查看 Qdrant 日志
python manage_services.py logs --services qdrant
```

## 📁 配置文件说明

### docker-compose.yml
完整的服务配置，包含：
- Qdrant 向量数据库
- Milvus 向量数据库（包含 etcd 和 minio 依赖）
- Redis 缓存
- PostgreSQL 数据库

### docker-compose.minimal.yml
最小化配置，只包含：
- Qdrant 向量数据库
- Milvus 向量数据库（单机版，内嵌 etcd）

## 🔧 服务配置

### Qdrant 配置
- **REST API**: http://localhost:6333
- **gRPC API**: localhost:6334
- **Web UI**: http://localhost:6333/dashboard
- **数据存储**: Docker volume `qdrant_data`

### Milvus 配置
- **gRPC API**: localhost:19530
- **Metrics**: http://localhost:9091
- **数据存储**: Docker volume `milvus_data`

### Redis 配置（可选）
- **端口**: 6379
- **数据存储**: Docker volume `redis_data`

### PostgreSQL 配置（可选）
- **端口**: 5432
- **数据库**: benchmark_db
- **用户**: benchmark_user
- **密码**: benchmark_pass
- **数据存储**: Docker volume `postgres_data`

## 🔍 健康检查

所有服务都配置了健康检查：

```bash
# 检查 Qdrant 健康状态
curl http://localhost:6333/health

# 检查 Milvus 健康状态
curl http://localhost:9091/healthz

# 检查 Redis 健康状态
redis-cli ping

# 检查 PostgreSQL 健康状态
pg_isready -h localhost -p 5432 -U benchmark_user
```

## 📊 数据持久化

所有服务数据都存储在 Docker volumes 中：

```bash
# 查看所有 volumes
docker volume ls | grep benchmark

# 查看 volume 详情
docker volume inspect services_qdrant_data
docker volume inspect services_milvus_data

# 备份数据（示例）
docker run --rm -v services_qdrant_data:/data -v $(pwd):/backup alpine tar czf /backup/qdrant_backup.tar.gz -C /data .

# 恢复数据（示例）
docker run --rm -v services_qdrant_data:/data -v $(pwd):/backup alpine tar xzf /backup/qdrant_backup.tar.gz -C /data
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :6333
   lsof -i :19530
   
   # 修改 docker-compose.yml 中的端口映射
   ```

2. **服务启动失败**
   ```bash
   # 查看详细日志
   python manage_services.py logs --services qdrant
   
   # 重新构建并启动
   docker-compose down
   docker-compose up -d --force-recreate
   ```

3. **数据丢失**
   ```bash
   # 检查 volume 是否存在
   docker volume ls
   
   # 确保没有使用 --remove-volumes 选项
   ```

4. **内存不足**
   ```bash
   # 检查 Docker 内存限制
   docker stats
   
   # 增加 Docker Desktop 内存分配
   ```

## 🔗 与项目集成

服务启动后，确保 `.env` 文件中的配置与服务端口匹配：

```env
# Milvus
MILVUS_API_URL = http://localhost:19530
MILVUS_DATABASE = milvus_benchmark
MILVUS_PWD = xxxxx

# Qdrant
QDRANT_API_URL = http://localhost:6333
QDRANT_DATABASE = qdrant_benchmark
QDRANT_PWD = xxxxx
```

然后在代码中使用：

```python
from services.settings.settings import settings

# 验证服务配置
settings.validate_milvus_settings()
settings.validate_qdrant_settings()

print(f"Milvus URL: {settings.milvus_api_url}")
print(f"Qdrant URL: {settings.qdrant_api_url}")
```

## 🧪 服务测试

### 健康检查

```bash
# 检查所有服务健康状态
python health_check.py

# 包括可选服务
python health_check.py --include-optional

# 等待服务启动完成
python health_check.py --wait 120

# 检查配置兼容性
python health_check.py --check-config
```

### 功能测试

```bash
# 测试所有核心服务功能
python test_services.py

# 包括可选服务测试
python test_services.py --include-optional

# 只测试特定服务
python test_services.py --service qdrant
python test_services.py --service milvus
```

### 使用 Makefile

```bash
# 查看所有可用命令
make help

# 启动服务并检查健康状态
make start
make health

# 启动最小化服务
make start-minimal

# 查看服务状态
make status

# 查看服务日志
make logs

# 备份数据
make backup

# 清理所有数据
make clean
```

## 📦 依赖库安装

为了使用完整的测试功能，需要安装以下 Python 库：

```bash
# 核心依赖
pip install qdrant-client pymilvus

# 可选依赖（用于测试可选服务）
pip install redis psycopg2-binary

# 或者安装所有依赖
pip install qdrant-client pymilvus redis psycopg2-binary requests
```

## 📝 注意事项

1. **首次启动**: Milvus 服务首次启动可能需要较长时间，请耐心等待
2. **资源需求**: 向量数据库服务需要较多内存，建议至少 4GB 可用内存
3. **数据备份**: 重要数据请定期备份 Docker volumes
4. **网络安全**: 生产环境请配置适当的网络安全策略
5. **依赖库**: 确保安装了相应的 Python 客户端库才能进行功能测试
