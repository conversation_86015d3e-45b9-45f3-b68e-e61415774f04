from abc import ABC, abstractmethod
from services.types.language import SuffixLanguage

from services.chunks.base_line_chunk import BaseLineChunk

class IChunk(ABC):
    @abstractmethod
    def chunk_file(file_context: str, language: SuffixLanguage = SuffixLanguage.TEXT, **kwargs):
        pass

    @staticmethod
    def getChunkService(name: str = "line"):
        if name == "line":
            return BaseLineChunk()
