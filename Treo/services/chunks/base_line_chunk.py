from services.chunks.IChunk import IChunk
from services.types.language import SuffixLanguage


class BaseLineChunk(IChunk):
    def chunk_file(file_content: str, language: SuffixLanguage = SuffixLanguage.TEXT, window_size: int = 30, overflow_size: int = 5):
        # 移除代码中的空白行
        lines = [line for line in file_content.split('\n') if line.strip()]
        
        chunks = []
        # 按照windo_size和overflow_size进行分块
        for i in range(0, len(lines), window_size - overflow_size):
            chunks.append(
                "\n".join(lines[i : i + window_size])
            )
            
        return chunks
    