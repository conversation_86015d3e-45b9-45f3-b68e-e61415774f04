#!/usr/bin/env python3
"""
简化的 dependencies 模块测试
不依赖外部库，只测试基本功能
"""

import os
import sys
import unittest

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from services.dependencies.database.vector_database import IVectorDatabase
from services.dependencies.database.qdrant_vector_database import QdrantVectorDatabase
from services.dependencies.database.milvus_vector_database import MilvusVectorDatabase
from services.dependencies.apis.llm import _format_messages, _format_tools
from services.types.tools import Tool

class TestVectorDatabaseInterface(unittest.TestCase):
    """测试向量数据库接口"""
    
    def test_interface_methods(self):
        """测试接口方法是否正确定义"""
        # 检查抽象方法
        abstract_methods = IVectorDatabase.__abstractmethods__
        expected_methods = {
            'connect', 'disconnect', 'create_collection', 'delete_collection',
            'collection_exists', 'get_collection', 'insert_vectors', 
            'search_by_vector', 'delete_vectors', 'get_collection_info'
        }
        
        self.assertEqual(abstract_methods, expected_methods)

class TestQdrantVectorDatabase(unittest.TestCase):
    """测试 Qdrant 向量数据库实现"""
    
    def setUp(self):
        self.db = QdrantVectorDatabase(
            api_url="http://localhost:6333",
            database="test_db",
            pwd="test_pwd"
        )
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.db.host, "localhost")
        self.assertEqual(self.db.port, 6333)
        self.assertEqual(self.db.database, "test_db")
        self.assertEqual(self.db.pwd, "test_pwd")
    
    def test_parse_api_url(self):
        """测试URL解析"""
        # 测试带协议的URL
        db1 = QdrantVectorDatabase("http://example.com:8080", "db", "pwd")
        self.assertEqual(db1.host, "example.com")
        self.assertEqual(db1.port, 8080)
        
        # 测试不带端口的URL
        db2 = QdrantVectorDatabase("https://example.com", "db", "pwd")
        self.assertEqual(db2.host, "example.com")
        self.assertEqual(db2.port, 6333)  # 默认端口

class TestMilvusVectorDatabase(unittest.TestCase):
    """测试 Milvus 向量数据库实现"""
    
    def setUp(self):
        self.db = MilvusVectorDatabase(
            api_url="http://localhost:19530",
            database="test_db",
            pwd="test_pwd"
        )
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.db.host, "localhost")
        self.assertEqual(self.db.port, "19530")
        self.assertEqual(self.db.database, "test_db")
        self.assertEqual(self.db.pwd, "test_pwd")
        self.assertFalse(self.db._connected)
    
    def test_parse_api_url(self):
        """测试URL解析"""
        # 测试带协议的URL
        db1 = MilvusVectorDatabase("http://example.com:8080", "db", "pwd")
        self.assertEqual(db1.host, "example.com")
        self.assertEqual(db1.port, "8080")
        
        # 测试不带端口的URL
        db2 = MilvusVectorDatabase("https://example.com", "db", "pwd")
        self.assertEqual(db2.host, "example.com")
        self.assertEqual(db2.port, "19530")  # 默认端口

class TestLLMAPIHelpers(unittest.TestCase):
    """测试 LLM API 辅助函数"""
    
    def setUp(self):
        self.test_tools = [
            Tool(
                name="test_tool",
                description="A test tool",
                func=lambda x: x,
                parameters={
                    "type": "object",
                    "properties": {
                        "param": {"type": "string", "description": "A parameter"}
                    },
                    "required": ["param"]
                }
            )
        ]
    
    def test_format_messages(self):
        """测试消息格式化"""
        query = "Hello"
        messages = ["Hi", "Hello there", "How are you?"]
        
        result = _format_messages(query, messages)
        
        expected = [
            {"role": "user", "content": "Hi"},
            {"role": "assistant", "content": "Hello there"},
            {"role": "user", "content": "How are you?"},
            {"role": "user", "content": "Hello"}
        ]
        
        self.assertEqual(result, expected)
    
    def test_format_messages_empty(self):
        """测试空消息列表格式化"""
        query = "Hello"
        messages = []
        
        result = _format_messages(query, messages)
        
        expected = [
            {"role": "user", "content": "Hello"}
        ]
        
        self.assertEqual(result, expected)
    
    def test_format_tools(self):
        """测试工具格式化"""
        result = _format_tools(self.test_tools)
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["type"], "function")
        self.assertEqual(result[0]["function"]["name"], "test_tool")
        self.assertEqual(result[0]["function"]["description"], "A test tool")
        self.assertIn("parameters", result[0]["function"])
    
    def test_format_tools_empty(self):
        """测试空工具列表格式化"""
        result = _format_tools([])
        
        self.assertEqual(result, [])

class TestToolClass(unittest.TestCase):
    """测试 Tool 类"""
    
    def test_tool_creation(self):
        """测试工具创建"""
        def test_func(x):
            return x * 2
        
        tool = Tool(
            name="multiply",
            description="Multiply by 2",
            func=test_func
        )
        
        self.assertEqual(tool.name, "multiply")
        self.assertEqual(tool.description, "Multiply by 2")
        self.assertEqual(tool.func(5), 10)
        self.assertIsNone(tool.parameters)
    
    def test_tool_with_parameters(self):
        """测试带参数的工具创建"""
        def test_func(x, y):
            return x + y
        
        parameters = {
            "type": "object",
            "properties": {
                "x": {"type": "integer"},
                "y": {"type": "integer"}
            },
            "required": ["x", "y"]
        }
        
        tool = Tool(
            name="add",
            description="Add two numbers",
            func=test_func,
            parameters=parameters
        )
        
        self.assertEqual(tool.name, "add")
        self.assertEqual(tool.description, "Add two numbers")
        self.assertEqual(tool.func(3, 4), 7)
        self.assertEqual(tool.parameters, parameters)

def run_tests():
    """运行所有测试"""
    print("=== 运行 Dependencies 模块简化测试 ===\n")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestVectorDatabaseInterface,
        TestQdrantVectorDatabase,
        TestMilvusVectorDatabase,
        TestLLMAPIHelpers,
        TestToolClass
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 打印结果
    print(f"\n=== 测试结果 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = result.wasSuccessful()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠️  部分测试失败")
    
    return success

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
