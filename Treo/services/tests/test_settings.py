#!/usr/bin/env python3
"""
测试 Settings 类的功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.settings.settings import Settings, get_settings, settings

def test_settings_loading():
    """测试设置加载功能"""
    print("=== 测试 Settings 类 ===")
    
    # 测试直接实例化
    print("\n1. 直接实例化 Settings:")
    direct_settings = Settings()
    print(f"百度 APP ID: {direct_settings.baidu_app_id}")
    print(f"模型 API: {direct_settings.model_api}")
    print(f"嵌入模型: {direct_settings.embedding}")
    print(f"Milvus URL: {direct_settings.milvus_api_url}")
    print(f"Chunk 类型: {direct_settings.chunk_type}")
    print(f"Chunk 窗口大小: {direct_settings.chunk_window_size}")
    
    # 测试单例模式
    print("\n2. 使用 get_settings() 函数:")
    singleton_settings = get_settings()
    print(f"百度 APP ID: {singleton_settings.baidu_app_id}")
    print(f"模型 API: {singleton_settings.model_api}")
    
    # 测试全局实例
    print("\n3. 使用全局 settings 实例:")
    print(f"百度 APP ID: {settings.baidu_app_id}")
    print(f"模型 API: {settings.model_api}")
    
    # 验证单例模式
    print(f"\n4. 验证单例模式:")
    print(f"direct_settings is singleton_settings: {direct_settings is not singleton_settings}")
    print(f"singleton_settings is settings: {singleton_settings is settings}")
    
    return direct_settings

def test_validation():
    """测试验证功能"""
    print("\n=== 测试验证功能 ===")
    
    test_settings = Settings()
    
    # 测试各种验证方法
    validation_methods = [
        ("百度翻译设置", test_settings.validate_required_settings),
        ("模型设置", test_settings.validate_model_settings),
        ("嵌入模型设置", test_settings.validate_embedding_settings),
        ("Milvus设置", test_settings.validate_milvus_settings),
        ("Qdrant设置", test_settings.validate_qdrant_settings),
        ("Chunk设置", test_settings.validate_chunk_settings),
    ]
    
    for name, method in validation_methods:
        try:
            result = method()
            print(f"✓ {name}: 验证通过")
        except ValueError as e:
            print(f"✗ {name}: {e}")
        except Exception as e:
            print(f"✗ {name}: 未知错误 - {e}")

def display_all_settings():
    """显示所有设置"""
    print("\n=== 所有环境变量设置 ===")
    
    test_settings = Settings()
    
    # 百度翻译API配置
    print("\n百度翻译API配置:")
    print(f"  BAIDU_APP_ID: {test_settings.baidu_app_id}")
    print(f"  BAIDU_APP_KEY: {test_settings.baidu_app_key}")
    print(f"  BAIDU_ENDPOINT: {test_settings.baidu_endpoint}")
    print(f"  BAIDU_TRANSLATE_PATH: {test_settings.baidu_translate_path}")
    
    # JoyBuilder API配置
    print("\nJoyBuilder 模型API配置:")
    print(f"  MODEL_APIKEY: {test_settings.model_apikey}")
    print(f"  MODEL_API: {test_settings.model_api}")
    print(f"  MODEL: {test_settings.model}")
    
    print("\nJoyBuilder 嵌入模型API配置:")
    print(f"  EMBEDDING_APIKEY: {test_settings.embedding_apikey}")
    print(f"  EMBEDDING_API: {test_settings.embedding_api}")
    print(f"  EMBEDDING: {test_settings.embedding}")
    
    # Milvus配置
    print("\nMilvus配置:")
    print(f"  MILVUS_API_URL: {test_settings.milvus_api_url}")
    print(f"  MILVUS_DATABASE: {test_settings.milvus_database}")
    print(f"  MILVUS_PWD: {test_settings.milvus_pwd}")
    
    # Qdrant配置
    print("\nQdrant配置:")
    print(f"  QDRANT_API_URL: {test_settings.qdrant_api_url}")
    print(f"  QDRANT_DATABASE: {test_settings.qdrant_database}")
    print(f"  QDRANT_PWD: {test_settings.qdrant_pwd}")
    
    # Chunk配置
    print("\nChunk配置:")
    print(f"  CHUNK_TYPE: {test_settings.chunk_type}")
    print(f"  CHUNK_WINDOW_SIZE: {test_settings.chunk_window_size}")
    print(f"  CHUNK_OVERFLOW_SIZE: {test_settings.chunk_overflow_size}")

if __name__ == "__main__":
    # 运行测试
    test_settings_loading()
    test_validation()
    display_all_settings()
    
    print("\n=== 测试完成 ===")
