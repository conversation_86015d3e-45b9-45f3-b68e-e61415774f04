#!/usr/bin/env python3
"""
测试 dependencies 模块中的所有实现
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from services.dependencies.database.vector_database import IVectorDatabase
from services.dependencies.database.qdrant_vector_database import QdrantVectorDatabase
from services.dependencies.database.milvus_vector_database import MilvusVectorDatabase
from services.dependencies.apis.llm import call_llm, _format_messages, _format_tools
from services.types.tools import Tool

class TestVectorDatabaseInterface(unittest.TestCase):
    """测试向量数据库接口"""
    
    def test_interface_methods(self):
        """测试接口方法是否正确定义"""
        # 检查抽象方法
        abstract_methods = IVectorDatabase.__abstractmethods__
        expected_methods = {
            'connect', 'disconnect', 'create_collection', 'delete_collection',
            'collection_exists', 'get_collection', 'insert_vectors', 
            'search_by_vector', 'delete_vectors', 'get_collection_info'
        }
        
        self.assertEqual(abstract_methods, expected_methods)

class TestQdrantVectorDatabase(unittest.TestCase):
    """测试 Qdrant 向量数据库实现"""
    
    def setUp(self):
        self.db = QdrantVectorDatabase(
            api_url="http://localhost:6333",
            database="test_db",
            pwd="test_pwd"
        )
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.db.host, "localhost")
        self.assertEqual(self.db.port, 6333)
        self.assertEqual(self.db.database, "test_db")
        self.assertEqual(self.db.pwd, "test_pwd")
    
    def test_parse_api_url(self):
        """测试URL解析"""
        # 测试带协议的URL
        db1 = QdrantVectorDatabase("http://example.com:8080", "db", "pwd")
        self.assertEqual(db1.host, "example.com")
        self.assertEqual(db1.port, 8080)
        
        # 测试不带端口的URL
        db2 = QdrantVectorDatabase("https://example.com", "db", "pwd")
        self.assertEqual(db2.host, "example.com")
        self.assertEqual(db2.port, 6333)  # 默认端口
    
    @patch('qdrant_client.QdrantClient')
    def test_connect_success(self, mock_client_class):
        """测试成功连接"""
        mock_client = Mock()
        mock_client.get_collections.return_value = Mock()
        mock_client_class.return_value = mock_client

        result = self.db.connect()

        self.assertTrue(result)
        self.assertIsNotNone(self.db.client)
        mock_client_class.assert_called_once()

    @patch('qdrant_client.QdrantClient')
    def test_connect_failure(self, mock_client_class):
        """测试连接失败"""
        mock_client_class.side_effect = Exception("Connection failed")

        result = self.db.connect()

        self.assertFalse(result)
        self.assertIsNone(self.db.client)

class TestMilvusVectorDatabase(unittest.TestCase):
    """测试 Milvus 向量数据库实现"""
    
    def setUp(self):
        self.db = MilvusVectorDatabase(
            api_url="http://localhost:19530",
            database="test_db",
            pwd="test_pwd"
        )
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.db.host, "localhost")
        self.assertEqual(self.db.port, "19530")
        self.assertEqual(self.db.database, "test_db")
        self.assertEqual(self.db.pwd, "test_pwd")
        self.assertFalse(self.db._connected)
    
    def test_parse_api_url(self):
        """测试URL解析"""
        # 测试带协议的URL
        db1 = MilvusVectorDatabase("http://example.com:8080", "db", "pwd")
        self.assertEqual(db1.host, "example.com")
        self.assertEqual(db1.port, "8080")
        
        # 测试不带端口的URL
        db2 = MilvusVectorDatabase("https://example.com", "db", "pwd")
        self.assertEqual(db2.host, "example.com")
        self.assertEqual(db2.port, "19530")  # 默认端口
    
    @patch('pymilvus.connections')
    @patch('pymilvus.utility')
    def test_connect_success(self, mock_utility, mock_connections):
        """测试成功连接"""
        mock_utility.list_collections.return_value = []

        result = self.db.connect()

        self.assertTrue(result)
        self.assertTrue(self.db._connected)
        mock_connections.connect.assert_called_once()

    @patch('pymilvus.connections')
    def test_connect_failure(self, mock_connections):
        """测试连接失败"""
        mock_connections.connect.side_effect = Exception("Connection failed")

        result = self.db.connect()

        self.assertFalse(result)
        self.assertFalse(self.db._connected)

class TestLLMAPI(unittest.TestCase):
    """测试 LLM API 调用"""
    
    def setUp(self):
        self.test_tools = [
            Tool(
                name="test_tool",
                description="A test tool",
                func=lambda x: x
            )
        ]
    
    def test_format_messages(self):
        """测试消息格式化"""
        query = "Hello"
        messages = ["Hi", "Hello there", "How are you?"]
        
        result = _format_messages(query, messages)
        
        expected = [
            {"role": "user", "content": "Hi"},
            {"role": "assistant", "content": "Hello there"},
            {"role": "user", "content": "How are you?"},
            {"role": "user", "content": "Hello"}
        ]
        
        self.assertEqual(result, expected)
    
    def test_format_tools(self):
        """测试工具格式化"""
        result = _format_tools(self.test_tools)
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["type"], "function")
        self.assertEqual(result[0]["function"]["name"], "test_tool")
        self.assertEqual(result[0]["function"]["description"], "A test tool")
    
    @patch('services.dependencies.apis.llm.settings')
    @patch('services.dependencies.apis.llm.requests.post')
    def test_call_llm_success(self, mock_post, mock_settings):
        """测试成功调用LLM"""
        # 模拟设置
        mock_settings.validate_model_settings.return_value = True
        mock_settings.model_api = "http://test-api.com"
        mock_settings.model_apikey = "test-key"
        mock_settings.model = "test-model"
        
        # 模拟响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "Test response"
                    }
                }
            ]
        }
        mock_post.return_value = mock_response
        
        result = call_llm("Hello", [], [])
        
        self.assertEqual(result, "Test response")
        mock_post.assert_called_once()
    
    @patch('services.dependencies.apis.llm.settings')
    @patch('services.dependencies.apis.llm.requests.post')
    def test_call_llm_with_tools(self, mock_post, mock_settings):
        """测试带工具的LLM调用"""
        # 模拟设置
        mock_settings.validate_model_settings.return_value = True
        mock_settings.model_api = "http://test-api.com"
        mock_settings.model_apikey = "test-key"
        mock_settings.model = "test-model"
        
        # 模拟响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "I'll use the tool",
                        "tool_calls": [
                            {
                                "id": "call_123",
                                "type": "function",
                                "function": {
                                    "name": "test_tool",
                                    "arguments": "{\"param\": \"value\"}"
                                }
                            }
                        ]
                    }
                }
            ]
        }
        mock_post.return_value = mock_response
        
        result = call_llm("Use the tool", [], self.test_tools)
        
        # 结果应该是JSON字符串，包含内容和工具调用
        import json
        parsed_result = json.loads(result)
        self.assertEqual(parsed_result["content"], "I'll use the tool")
        self.assertEqual(len(parsed_result["tool_calls"]), 1)
    
    @patch('services.dependencies.apis.llm.settings')
    def test_call_llm_settings_error(self, mock_settings):
        """测试设置验证失败"""
        mock_settings.validate_model_settings.side_effect = ValueError("Missing settings")
        
        result = call_llm("Hello", [], [])
        
        self.assertTrue(result.startswith("Error:"))

def run_tests():
    """运行所有测试"""
    print("=== 运行 Dependencies 模块测试 ===\n")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestVectorDatabaseInterface,
        TestQdrantVectorDatabase,
        TestMilvusVectorDatabase,
        TestLLMAPI
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 打印结果
    print(f"\n=== 测试结果 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
