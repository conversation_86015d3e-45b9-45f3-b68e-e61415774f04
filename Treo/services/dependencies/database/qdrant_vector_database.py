from typing import List, Dict, Any, Optional
from services.dependencies.database.vector_database import IVectorDatabase
import logging

logger = logging.getLogger(__name__)

class QdrantVectorDatabase(IVectorDatabase):
    """Qdrant 向量数据库实现"""

    def __init__(self, api_url: str, database: str, pwd: str, **kwargs):
        super().__init__(api_url, database, pwd, **kwargs)
        self.client = None
        self._parse_api_url()

    def _parse_api_url(self):
        """解析API URL获取主机和端口"""
        if self.api_url.startswith('http://'):
            url_without_protocol = self.api_url[7:]
        elif self.api_url.startswith('https://'):
            url_without_protocol = self.api_url[8:]
        else:
            url_without_protocol = self.api_url

        if ':' in url_without_protocol:
            self.host, port_str = url_without_protocol.split(':')
            self.port = int(port_str)
        else:
            self.host = url_without_protocol
            self.port = 6333  # Qdrant 默认端口

    def connect(self) -> bool:
        """连接到 Qdrant 数据库"""
        try:
            from qdrant_client import QdrantClient

            self.client = QdrantClient(
                host=self.host,
                port=self.port,
                api_key=self.pwd if self.pwd and self.pwd != 'xxxxx' else None,
                timeout=self.config.get('timeout', 30)
            )

            # 测试连接
            collections = self.client.get_collections()
            logger.info(f"Successfully connected to Qdrant at {self.host}:{self.port}")
            return True

        except ImportError:
            logger.error("qdrant-client library not installed. Please install it with: pip install qdrant-client")
            return False
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {str(e)}")
            return False

    def disconnect(self):
        """断开 Qdrant 连接"""
        if self.client:
            try:
                self.client.close()
                logger.info("Disconnected from Qdrant")
            except Exception as e:
                logger.warning(f"Error disconnecting from Qdrant: {str(e)}")
        self.client = None

    def create_collection(self, collection_name: str, vector_dim: int, **kwargs) -> bool:
        """创建 Qdrant 集合"""
        if not self.client:
            if not self.connect():
                return False

        try:
            from qdrant_client.models import Distance, VectorParams

            # 获取距离度量方式，默认为余弦相似度
            distance = kwargs.get('distance', Distance.COSINE)
            if isinstance(distance, str):
                distance_map = {
                    'cosine': Distance.COSINE,
                    'euclidean': Distance.EUCLID,
                    'dot': Distance.DOT
                }
                distance = distance_map.get(distance.lower(), Distance.COSINE)

            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(size=vector_dim, distance=distance)
            )

            logger.info(f"Successfully created collection '{collection_name}' with dimension {vector_dim}")
            return True

        except Exception as e:
            logger.error(f"Failed to create collection '{collection_name}': {str(e)}")
            return False

    def delete_collection(self, collection_name: str) -> bool:
        """删除 Qdrant 集合"""
        if not self.client:
            if not self.connect():
                return False

        try:
            self.client.delete_collection(collection_name)
            logger.info(f"Successfully deleted collection '{collection_name}'")
            return True

        except Exception as e:
            logger.error(f"Failed to delete collection '{collection_name}': {str(e)}")
            return False

    def collection_exists(self, collection_name: str) -> bool:
        """检查 Qdrant 集合是否存在"""
        if not self.client:
            if not self.connect():
                return False

        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            return collection_name in collection_names

        except Exception as e:
            logger.error(f"Failed to check if collection '{collection_name}' exists: {str(e)}")
            return False

    def get_collection(self, collection_name: str) -> Any:
        """获取 Qdrant 集合实例"""
        if not self.client:
            if not self.connect():
                return None

        try:
            if self.collection_exists(collection_name):
                return self.client
            else:
                logger.warning(f"Collection '{collection_name}' does not exist")
                return None

        except Exception as e:
            logger.error(f"Failed to get collection '{collection_name}': {str(e)}")
            return None

    def insert_vectors(self, collection_name: str, vectors: List[List[float]],
                      ids: Optional[List[str]] = None, metadata: Optional[List[Dict]] = None) -> bool:
        """插入向量数据到 Qdrant"""
        if not self.client:
            if not self.connect():
                return False

        try:
            from qdrant_client.models import PointStruct

            # 准备数据点
            points = []
            for i, vector in enumerate(vectors):
                point_id = ids[i] if ids else i
                payload = metadata[i] if metadata else {}

                points.append(PointStruct(
                    id=point_id,
                    vector=vector,
                    payload=payload
                ))

            # 插入数据
            self.client.upsert(
                collection_name=collection_name,
                points=points
            )

            logger.info(f"Successfully inserted {len(vectors)} vectors into collection '{collection_name}'")
            return True

        except Exception as e:
            logger.error(f"Failed to insert vectors into collection '{collection_name}': {str(e)}")
            return False

    def search_by_vector(self, collection_name: str, query_vector: List[float],
                        search_params: Dict, top_k: int = 10) -> List[Dict]:
        """在 Qdrant 中进行向量搜索"""
        if not self.client:
            if not self.connect():
                return []

        try:
            # 执行搜索
            search_result = self.client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                limit=top_k,
                with_payload=search_params.get('with_payload', True),
                with_vectors=search_params.get('with_vectors', False),
                score_threshold=search_params.get('score_threshold', None)
            )

            # 转换结果格式
            results = []
            for hit in search_result:
                result = {
                    'id': hit.id,
                    'score': hit.score,
                    'payload': hit.payload if hasattr(hit, 'payload') else {}
                }
                if hasattr(hit, 'vector') and hit.vector:
                    result['vector'] = hit.vector
                results.append(result)

            logger.info(f"Search completed, found {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Failed to search in collection '{collection_name}': {str(e)}")
            return []

    def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """删除 Qdrant 中的向量数据"""
        if not self.client:
            if not self.connect():
                return False

        try:
            self.client.delete(
                collection_name=collection_name,
                points_selector=ids
            )

            logger.info(f"Successfully deleted {len(ids)} vectors from collection '{collection_name}'")
            return True

        except Exception as e:
            logger.error(f"Failed to delete vectors from collection '{collection_name}': {str(e)}")
            return False

    def get_collection_info(self, collection_name: str) -> Dict:
        """获取 Qdrant 集合信息"""
        if not self.client:
            if not self.connect():
                return {}

        try:
            collection_info = self.client.get_collection(collection_name)

            info = {
                'name': collection_name,
                'vectors_count': collection_info.vectors_count,
                'indexed_vectors_count': collection_info.indexed_vectors_count,
                'points_count': collection_info.points_count,
                'segments_count': collection_info.segments_count,
                'status': collection_info.status.value if hasattr(collection_info.status, 'value') else str(collection_info.status),
                'optimizer_status': collection_info.optimizer_status.value if hasattr(collection_info.optimizer_status, 'value') else str(collection_info.optimizer_status),
                'config': {
                    'vector_size': collection_info.config.params.vectors.size,
                    'distance': collection_info.config.params.vectors.distance.value if hasattr(collection_info.config.params.vectors.distance, 'value') else str(collection_info.config.params.vectors.distance)
                }
            }

            return info

        except Exception as e:
            logger.error(f"Failed to get info for collection '{collection_name}': {str(e)}")
            return {}