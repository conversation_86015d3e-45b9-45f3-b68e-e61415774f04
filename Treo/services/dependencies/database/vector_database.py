from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional


class IVectorDatabase(ABC):
    """向量数据库抽象基类"""

    def __init__(self, api_url: str, database: str, pwd: str, **kwargs):
        """
        初始化向量数据库连接

        Args:
            api_url: 数据库API地址
            database: 数据库名称
            pwd: 数据库密码
            **kwargs: 其他配置参数
        """
        self.api_url = api_url
        self.database = database
        self.pwd = pwd
        self.config = kwargs

    @abstractmethod
    def connect(self) -> bool:
        """
        连接到向量数据库

        Returns:
            bool: 连接是否成功
        """
        pass

    @abstractmethod
    def disconnect(self):
        """断开数据库连接"""
        pass

    @abstractmethod
    def create_collection(self, collection_name: str, vector_dim: int, **kwargs) -> bool:
        """
        创建集合

        Args:
            collection_name: 集合名称
            vector_dim: 向量维度
            **kwargs: 其他参数

        Returns:
            bool: 创建是否成功
        """
        pass

    @abstractmethod
    def delete_collection(self, collection_name: str) -> bool:
        """
        删除集合

        Args:
            collection_name: 集合名称

        Returns:
            bool: 删除是否成功
        """
        pass

    @abstractmethod
    def collection_exists(self, collection_name: str) -> bool:
        """
        检查集合是否存在

        Args:
            collection_name: 集合名称

        Returns:
            bool: 集合是否存在
        """
        pass

    @abstractmethod
    def get_collection(self, collection_name: str) -> Any:
        """
        获取集合实例

        Args:
            collection_name: 集合名称

        Returns:
            集合实例
        """
        pass

    @abstractmethod
    def insert_vectors(self, collection_name: str, vectors: List[List[float]],
                      ids: Optional[List[str]] = None, metadata: Optional[List[Dict]] = None) -> bool:
        """
        插入向量数据

        Args:
            collection_name: 集合名称
            vectors: 向量列表
            ids: 向量ID列表
            metadata: 元数据列表

        Returns:
            bool: 插入是否成功
        """
        pass

    @abstractmethod
    def search_by_vector(self, collection_name: str, query_vector: List[float],
                        search_params: Dict, top_k: int = 10) -> List[Dict]:
        """
        根据向量进行搜索

        Args:
            collection_name: 集合名称
            query_vector: 查询向量
            search_params: 搜索参数
            top_k: 返回结果数量

        Returns:
            List[Dict]: 搜索结果列表
        """
        pass

    @abstractmethod
    def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """
        删除向量数据

        Args:
            collection_name: 集合名称
            ids: 要删除的向量ID列表

        Returns:
            bool: 删除是否成功
        """
        pass

    @abstractmethod
    def get_collection_info(self, collection_name: str) -> Dict:
        """
        获取集合信息

        Args:
            collection_name: 集合名称

        Returns:
            Dict: 集合信息
        """
        pass
