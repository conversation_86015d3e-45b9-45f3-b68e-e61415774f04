from typing import List, Dict, Any, Optional
from services.dependencies.database.vector_database import IVectorDatabase
import logging

logger = logging.getLogger(__name__)

class MilvusVectorDatabase(IVectorDatabase):
    """Milvus 向量数据库实现"""

    def __init__(self, api_url: str, database: str, pwd: str, **kwargs):
        super().__init__(api_url, database, pwd, **kwargs)
        self.connection_alias = kwargs.get('connection_alias', 'default')
        self._parse_api_url()
        self._connected = False

    def _parse_api_url(self):
        """解析API URL获取主机和端口"""
        if self.api_url.startswith('http://'):
            url_without_protocol = self.api_url[7:]
        elif self.api_url.startswith('https://'):
            url_without_protocol = self.api_url[8:]
        else:
            url_without_protocol = self.api_url

        if ':' in url_without_protocol:
            self.host, port_str = url_without_protocol.split(':')
            self.port = port_str
        else:
            self.host = url_without_protocol
            self.port = "19530"  # Milvus 默认端口

    def connect(self) -> bool:
        """连接到 Milvus 数据库"""
        try:
            from pymilvus import connections

            # 连接参数
            connect_params = {
                "alias": self.connection_alias,
                "host": self.host,
                "port": self.port
            }

            # 如果有密码且不是占位符，添加认证信息
            if self.pwd and self.pwd != 'xxxxx':
                connect_params["user"] = self.config.get('user', 'root')
                connect_params["password"] = self.pwd

            # 建立连接
            connections.connect(**connect_params)

            # 测试连接
            from pymilvus import utility
            utility.list_collections(using=self.connection_alias)

            self._connected = True
            logger.info(f"Successfully connected to Milvus at {self.host}:{self.port}")
            return True

        except ImportError:
            logger.error("pymilvus library not installed. Please install it with: pip install pymilvus")
            return False
        except Exception as e:
            logger.error(f"Failed to connect to Milvus: {str(e)}")
            return False

    def disconnect(self):
        """断开 Milvus 连接"""
        if self._connected:
            try:
                from pymilvus import connections
                connections.disconnect(self.connection_alias)
                self._connected = False
                logger.info("Disconnected from Milvus")
            except Exception as e:
                logger.warning(f"Error disconnecting from Milvus: {str(e)}")

    def create_collection(self, collection_name: str, vector_dim: int, **kwargs) -> bool:
        """创建 Milvus 集合"""
        if not self._connected:
            if not self.connect():
                return False

        try:
            from pymilvus import Collection, CollectionSchema, FieldSchema, DataType

            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=kwargs.get('auto_id', True)),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=vector_dim)
            ]

            # 添加额外的元数据字段
            if 'metadata_fields' in kwargs:
                for field_name, field_config in kwargs['metadata_fields'].items():
                    field_schema = FieldSchema(
                        name=field_name,
                        dtype=field_config.get('dtype', DataType.VARCHAR),
                        max_length=field_config.get('max_length', 100)
                    )
                    fields.append(field_schema)

            # 创建集合 schema
            schema = CollectionSchema(
                fields=fields,
                description=kwargs.get('description', f"Collection {collection_name}")
            )

            # 创建集合
            collection = Collection(
                name=collection_name,
                schema=schema,
                using=self.connection_alias
            )

            logger.info(f"Successfully created collection '{collection_name}' with dimension {vector_dim}")
            return True

        except Exception as e:
            logger.error(f"Failed to create collection '{collection_name}': {str(e)}")
            return False

    def delete_collection(self, collection_name: str) -> bool:
        """删除 Milvus 集合"""
        if not self._connected:
            if not self.connect():
                return False

        try:
            from pymilvus import utility

            if utility.has_collection(collection_name, using=self.connection_alias):
                utility.drop_collection(collection_name, using=self.connection_alias)
                logger.info(f"Successfully deleted collection '{collection_name}'")
                return True
            else:
                logger.warning(f"Collection '{collection_name}' does not exist")
                return True

        except Exception as e:
            logger.error(f"Failed to delete collection '{collection_name}': {str(e)}")
            return False

    def collection_exists(self, collection_name: str) -> bool:
        """检查 Milvus 集合是否存在"""
        if not self._connected:
            if not self.connect():
                return False

        try:
            from pymilvus import utility
            return utility.has_collection(collection_name, using=self.connection_alias)

        except Exception as e:
            logger.error(f"Failed to check if collection '{collection_name}' exists: {str(e)}")
            return False

    def get_collection(self, collection_name: str) -> Any:
        """获取 Milvus 集合实例"""
        if not self._connected:
            if not self.connect():
                return None

        try:
            from pymilvus import Collection

            if self.collection_exists(collection_name):
                collection = Collection(collection_name, using=self.connection_alias)
                return collection
            else:
                logger.warning(f"Collection '{collection_name}' does not exist")
                return None

        except Exception as e:
            logger.error(f"Failed to get collection '{collection_name}': {str(e)}")
            return None

    def insert_vectors(self, collection_name: str, vectors: List[List[float]],
                      ids: Optional[List[str]] = None, metadata: Optional[List[Dict]] = None) -> bool:
        """插入向量数据到 Milvus"""
        if not self._connected:
            if not self.connect():
                return False

        try:
            collection = self.get_collection(collection_name)
            if not collection:
                return False

            # 准备数据
            data = []

            # 添加ID字段（如果集合不是auto_id）
            if ids:
                # 将字符串ID转换为整数ID
                int_ids = [hash(id_str) % (2**63) for id_str in ids]
                data.append(int_ids)

            # 添加向量数据
            data.append(vectors)

            # 添加元数据字段
            if metadata:
                # 获取集合schema来确定字段
                schema = collection.schema
                for field in schema.fields:
                    if field.name not in ['id', 'vector']:
                        field_data = []
                        for meta in metadata:
                            field_data.append(meta.get(field.name, ""))
                        data.append(field_data)

            # 插入数据
            mr = collection.insert(data)

            logger.info(f"Successfully inserted {len(vectors)} vectors into collection '{collection_name}'")
            return True

        except Exception as e:
            logger.error(f"Failed to insert vectors into collection '{collection_name}': {str(e)}")
            return False

    def search_by_vector(self, collection_name: str, query_vector: List[float],
                        search_params: Dict, top_k: int = 10) -> List[Dict]:
        """在 Milvus 中进行向量搜索"""
        if not self._connected:
            if not self.connect():
                return []

        try:
            collection = self.get_collection(collection_name)
            if not collection:
                return []

            # 确保集合已加载
            collection.load()

            # 设置搜索参数
            search_params_milvus = {
                "metric_type": search_params.get('metric_type', 'L2'),
                "params": search_params.get('params', {"nprobe": 10})
            }

            # 获取输出字段
            output_fields = search_params.get('output_fields', ['*'])

            # 执行搜索
            results = collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params_milvus,
                limit=top_k,
                output_fields=output_fields
            )

            # 转换结果格式
            formatted_results = []
            if results and len(results) > 0:
                for hit in results[0]:
                    result = {
                        'id': str(hit.id),
                        'score': hit.distance,  # Milvus 返回的是距离，不是相似度分数
                        'entity': hit.entity.to_dict() if hasattr(hit, 'entity') else {}
                    }
                    formatted_results.append(result)

            logger.info(f"Search completed, found {len(formatted_results)} results")
            return formatted_results

        except Exception as e:
            logger.error(f"Failed to search in collection '{collection_name}': {str(e)}")
            return []

    def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """删除 Milvus 中的向量数据"""
        if not self._connected:
            if not self.connect():
                return False

        try:
            collection = self.get_collection(collection_name)
            if not collection:
                return False

            # 将字符串ID转换为整数ID
            int_ids = [hash(id_str) % (2**63) for id_str in ids]

            # 构建删除表达式
            id_expr = f"id in {int_ids}"

            # 删除数据
            collection.delete(id_expr)

            logger.info(f"Successfully deleted {len(ids)} vectors from collection '{collection_name}'")
            return True

        except Exception as e:
            logger.error(f"Failed to delete vectors from collection '{collection_name}': {str(e)}")
            return False

    def get_collection_info(self, collection_name: str) -> Dict:
        """获取 Milvus 集合信息"""
        if not self._connected:
            if not self.connect():
                return {}

        try:
            collection = self.get_collection(collection_name)
            if not collection:
                return {}

            # 获取集合统计信息
            stats = collection.get_stats()

            # 获取集合schema信息
            schema = collection.schema

            info = {
                'name': collection_name,
                'description': schema.description,
                'num_entities': int(stats['row_count']) if 'row_count' in stats else 0,
                'fields': [],
                'indexes': []
            }

            # 添加字段信息
            for field in schema.fields:
                field_info = {
                    'name': field.name,
                    'type': str(field.dtype),
                    'is_primary': field.is_primary,
                    'auto_id': field.auto_id if hasattr(field, 'auto_id') else False
                }
                if hasattr(field, 'dim'):
                    field_info['dimension'] = field.dim
                info['fields'].append(field_info)

            # 获取索引信息
            try:
                indexes = collection.indexes
                for index in indexes:
                    index_info = {
                        'field_name': index.field_name,
                        'index_name': index.index_name,
                        'params': index.params
                    }
                    info['indexes'].append(index_info)
            except:
                pass  # 索引信息获取失败时忽略

            return info

        except Exception as e:
            logger.error(f"Failed to get info for collection '{collection_name}': {str(e)}")
            return {}
