from typing import List, Dict, Any, Optional, Union
import json
import requests
import logging
from services.types.tools import Tool
from services.settings.settings import settings

logger = logging.getLogger(__name__)

def call_llm(query: str, messages: List[str], tools: List[Tool],
             is_stream: bool = False, temperature: float = 0.01,
             model: Optional[str] = None, **kwargs) -> Union[str, Dict]:
    """
    调用大语言模型API

    Args:
        query: 用户查询
        messages: 历史消息列表
        tools: 可用工具列表
        is_stream: 是否流式返回
        temperature: 温度参数，控制生成的随机性
        model: 指定使用的模型，如果为None则使用配置中的默认模型
        **kwargs: 其他参数

    Returns:
        str: 模型响应文本（非流式）
        Dict: 流式响应的生成器或完整响应对象
    """
    try:
        # 验证模型配置
        settings.validate_model_settings()

        # 准备请求参数
        api_url = settings.model_api
        api_key = settings.model_apikey
        model_name = model or settings.model

        # 构建消息格式
        formatted_messages = _format_messages(query, messages)

        # 构建工具定义
        tool_definitions = _format_tools(tools) if tools else []

        # 构建请求体
        request_data = {
            "model": model_name,
            "messages": formatted_messages,
            "temperature": temperature,
            "stream": is_stream
        }

        # 添加工具定义
        if tool_definitions:
            request_data["tools"] = tool_definitions
            request_data["tool_choice"] = kwargs.get("tool_choice", "auto")

        # 添加其他参数
        if "max_tokens" in kwargs:
            request_data["max_tokens"] = kwargs["max_tokens"]
        if "top_p" in kwargs:
            request_data["top_p"] = kwargs["top_p"]
        if "frequency_penalty" in kwargs:
            request_data["frequency_penalty"] = kwargs["frequency_penalty"]
        if "presence_penalty" in kwargs:
            request_data["presence_penalty"] = kwargs["presence_penalty"]

        # 准备请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        # 发送请求
        if is_stream:
            return _handle_stream_response(api_url, headers, request_data)
        else:
            return _handle_normal_response(api_url, headers, request_data)

    except Exception as e:
        logger.error(f"Failed to call LLM API: {str(e)}")
        if is_stream:
            return {"error": str(e)}
        else:
            return f"Error: {str(e)}"

def _format_messages(query: str, messages: List[str]) -> List[Dict[str, str]]:
    """格式化消息为API所需格式"""
    formatted_messages = []

    # 添加历史消息
    for i, message in enumerate(messages):
        role = "user" if i % 2 == 0 else "assistant"
        formatted_messages.append({
            "role": role,
            "content": message
        })

    # 添加当前查询
    formatted_messages.append({
        "role": "user",
        "content": query
    })

    return formatted_messages

def _format_tools(tools: List[Tool]) -> List[Dict[str, Any]]:
    """格式化工具定义为API所需格式"""
    tool_definitions = []

    for tool in tools:
        tool_def = {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }

        # 如果工具有参数定义，添加参数信息
        if hasattr(tool, 'parameters') and tool.parameters:
            tool_def["function"]["parameters"] = tool.parameters

        tool_definitions.append(tool_def)

    return tool_definitions

def _handle_normal_response(api_url: str, headers: Dict[str, str], request_data: Dict[str, Any]) -> str:
    """处理非流式响应"""
    try:
        # 构建完整的API端点
        endpoint = f"{api_url.rstrip('/')}/v1/chat/completions"

        response = requests.post(
            endpoint,
            headers=headers,
            json=request_data,
            timeout=60
        )

        response.raise_for_status()

        result = response.json()

        # 提取响应内容
        if "choices" in result and len(result["choices"]) > 0:
            choice = result["choices"][0]

            # 检查是否有工具调用
            if "tool_calls" in choice.get("message", {}):
                return json.dumps({
                    "content": choice["message"].get("content", ""),
                    "tool_calls": choice["message"]["tool_calls"]
                })
            else:
                return choice["message"].get("content", "")
        else:
            logger.warning("No choices in LLM response")
            return "No response from model"

    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP request failed: {str(e)}")
        return f"Request failed: {str(e)}"
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON response: {str(e)}")
        return f"Invalid JSON response: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error in normal response handling: {str(e)}")
        return f"Unexpected error: {str(e)}"

def _handle_stream_response(api_url: str, headers: Dict[str, str], request_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理流式响应"""
    try:
        # 构建完整的API端点
        endpoint = f"{api_url.rstrip('/')}/v1/chat/completions"

        response = requests.post(
            endpoint,
            headers=headers,
            json=request_data,
            stream=True,
            timeout=60
        )

        response.raise_for_status()

        def stream_generator():
            """流式响应生成器"""
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀

                        if data_str.strip() == '[DONE]':
                            break

                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue

        return {
            "stream": True,
            "generator": stream_generator(),
            "response": response
        }

    except requests.exceptions.RequestException as e:
        logger.error(f"Stream request failed: {str(e)}")
        return {"error": f"Stream request failed: {str(e)}"}
    except Exception as e:
        logger.error(f"Unexpected error in stream response handling: {str(e)}")
        return {"error": f"Unexpected error: {str(e)}"}

