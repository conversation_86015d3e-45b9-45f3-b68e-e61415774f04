# Dependencies 模块

这个模块包含了项目的外部依赖服务接口实现，包括向量数据库和大语言模型API调用。

## 📁 目录结构

```
dependencies/
├── README.md                    # 本文档
├── apis/
│   └── llm.py                  # 大语言模型API调用
└── database/
    ├── vector_database.py      # 向量数据库抽象接口
    ├── qdrant_vector_database.py  # Qdrant实现
    └── milvus_vector_database.py  # Milvus实现
```

## 🗄️ 向量数据库

### 抽象接口

所有向量数据库实现都继承自 `IVectorDatabase` 抽象基类：

```python
from services.dependencies.database.vector_database import IVectorDatabase

class IVectorDatabase(ABC):
    def connect(self) -> bool: ...
    def disconnect(self): ...
    def create_collection(self, collection_name: str, vector_dim: int, **kwargs) -> bool: ...
    def delete_collection(self, collection_name: str) -> bool: ...
    def collection_exists(self, collection_name: str) -> bool: ...
    def get_collection(self, collection_name: str) -> Any: ...
    def insert_vectors(self, collection_name: str, vectors: List[List[float]], 
                      ids: Optional[List[str]] = None, metadata: Optional[List[Dict]] = None) -> bool: ...
    def search_by_vector(self, collection_name: str, query_vector: List[float], 
                        search_params: Dict, top_k: int = 10) -> List[Dict]: ...
    def delete_vectors(self, collection_name: str, ids: List[str]) -> bool: ...
    def get_collection_info(self, collection_name: str) -> Dict: ...
```

### Qdrant 实现

```python
from services.dependencies.database.qdrant_vector_database import QdrantVectorDatabase

# 初始化
qdrant_db = QdrantVectorDatabase(
    api_url="http://localhost:6333",
    database="my_database",
    pwd="your_password"
)

# 连接
if qdrant_db.connect():
    print("Connected to Qdrant successfully")

# 创建集合
qdrant_db.create_collection(
    collection_name="my_collection",
    vector_dim=128,
    distance="cosine"  # 可选: cosine, euclidean, dot
)

# 插入向量
vectors = [[0.1, 0.2, ...], [0.3, 0.4, ...]]  # 128维向量
ids = ["doc1", "doc2"]
metadata = [{"text": "document 1"}, {"text": "document 2"}]

qdrant_db.insert_vectors("my_collection", vectors, ids, metadata)

# 搜索
query_vector = [0.1, 0.2, ...]  # 128维查询向量
search_params = {
    "with_payload": True,
    "score_threshold": 0.5
}

results = qdrant_db.search_by_vector("my_collection", query_vector, search_params, top_k=5)
for result in results:
    print(f"ID: {result['id']}, Score: {result['score']}, Payload: {result['payload']}")
```

### Milvus 实现

```python
from services.dependencies.database.milvus_vector_database import MilvusVectorDatabase

# 初始化
milvus_db = MilvusVectorDatabase(
    api_url="http://localhost:19530",
    database="my_database",
    pwd="your_password"
)

# 连接
if milvus_db.connect():
    print("Connected to Milvus successfully")

# 创建集合
milvus_db.create_collection(
    collection_name="my_collection",
    vector_dim=128,
    auto_id=True,  # 自动生成ID
    metadata_fields={
        "text": {"dtype": "VARCHAR", "max_length": 500}
    }
)

# 插入向量
vectors = [[0.1, 0.2, ...], [0.3, 0.4, ...]]  # 128维向量
metadata = [{"text": "document 1"}, {"text": "document 2"}]

milvus_db.insert_vectors("my_collection", vectors, metadata=metadata)

# 搜索
query_vector = [0.1, 0.2, ...]  # 128维查询向量
search_params = {
    "metric_type": "L2",
    "params": {"nprobe": 10},
    "output_fields": ["text"]
}

results = milvus_db.search_by_vector("my_collection", query_vector, search_params, top_k=5)
for result in results:
    print(f"ID: {result['id']}, Distance: {result['score']}, Entity: {result['entity']}")
```

## 🤖 大语言模型API

### 基本使用

```python
from services.dependencies.apis.llm import call_llm
from services.types.tools import Tool

# 简单调用
response = call_llm(
    query="你好，请介绍一下自己",
    messages=[],
    tools=[],
    temperature=0.7
)
print(response)
```

### 带历史消息的对话

```python
# 带历史消息的对话
messages = [
    "你好",
    "你好！我是AI助手，很高兴为您服务。",
    "请帮我写一个Python函数"
]

response = call_llm(
    query="这个函数用来计算斐波那契数列",
    messages=messages,
    tools=[],
    temperature=0.3
)
print(response)
```

### 使用工具调用

```python
# 定义工具
def calculate_sum(a: int, b: int) -> int:
    return a + b

tools = [
    Tool(
        name="calculate_sum",
        description="计算两个数的和",
        func=calculate_sum,
        parameters={
            "type": "object",
            "properties": {
                "a": {"type": "integer", "description": "第一个数"},
                "b": {"type": "integer", "description": "第二个数"}
            },
            "required": ["a", "b"]
        }
    )
]

response = call_llm(
    query="请帮我计算 15 + 27 的结果",
    messages=[],
    tools=tools,
    temperature=0.1
)

# 如果返回工具调用，response会是JSON格式
import json
if response.startswith('{'):
    result = json.loads(response)
    if 'tool_calls' in result:
        print("模型想要调用工具:", result['tool_calls'])
```

### 流式调用

```python
# 流式调用
stream_response = call_llm(
    query="请写一个关于春天的诗",
    messages=[],
    tools=[],
    is_stream=True,
    temperature=0.8
)

if stream_response.get("stream"):
    print("流式响应:")
    for chunk in stream_response["generator"]:
        print(chunk, end="", flush=True)
    print()  # 换行
else:
    print("错误:", stream_response.get("error"))
```

### 自定义参数

```python
response = call_llm(
    query="请简要回答这个问题：什么是机器学习？",
    messages=[],
    tools=[],
    temperature=0.5,
    max_tokens=200,
    top_p=0.9,
    frequency_penalty=0.1,
    presence_penalty=0.1,
    model="qwen3-coder"  # 指定特定模型
)
```

## ⚙️ 配置

确保在 `.env` 文件中配置了相应的服务参数：

```env
# Milvus
MILVUS_API_URL = http://localhost:19530
MILVUS_DATABASE = milvus_benchmark
MILVUS_PWD = your_password

# Qdrant
QDRANT_API_URL = http://localhost:6333
QDRANT_DATABASE = qdrant_benchmark
QDRANT_PWD = your_password

# JoyBuilder API
MODEL_APIKEY = your_api_key
MODEL_API = http://ai-api.jdcloud.com
MODEL = qwen3-coder
```

## 🧪 测试

运行测试以验证实现：

```bash
# 运行所有依赖模块测试
python services/tests/test_dependencies.py

# 或者使用unittest
python -m unittest services.tests.test_dependencies
```

## 📦 依赖库

确保安装了必要的依赖库：

```bash
# 向量数据库客户端
pip install qdrant-client pymilvus

# HTTP请求库
pip install requests

# 其他依赖
pip install pydantic
```

## 🔧 扩展

### 添加新的向量数据库

1. 继承 `IVectorDatabase` 抽象基类
2. 实现所有抽象方法
3. 添加相应的配置和测试

```python
from services.dependencies.database.vector_database import IVectorDatabase

class MyVectorDatabase(IVectorDatabase):
    def connect(self) -> bool:
        # 实现连接逻辑
        pass
    
    # 实现其他抽象方法...
```

### 自定义LLM API

修改 `llm.py` 中的实现以支持不同的API格式：

```python
def call_custom_llm(query: str, **kwargs):
    # 自定义实现
    pass
```

## 📝 注意事项

1. **错误处理**: 所有方法都包含了适当的错误处理和日志记录
2. **连接管理**: 记得在使用完毕后调用 `disconnect()` 方法
3. **资源清理**: 向量数据库操作可能消耗较多内存，注意及时清理
4. **API限制**: 注意LLM API的调用频率和token限制
5. **数据格式**: 确保向量数据的维度和格式正确
