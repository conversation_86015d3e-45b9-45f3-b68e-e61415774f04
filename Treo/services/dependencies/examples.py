#!/usr/bin/env python3
"""
Dependencies 模块使用示例
演示如何使用向量数据库和LLM API
"""

import os
import sys
import numpy as np
from typing import List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from services.dependencies.database.qdrant_vector_database import QdrantVectorDatabase
from services.dependencies.database.milvus_vector_database import MilvusVectorDatabase
from services.dependencies.apis.llm import call_llm
from services.types.tools import Tool
from services.settings.settings import settings

def example_qdrant_usage():
    """Qdrant 使用示例"""
    print("=== Qdrant 向量数据库使用示例 ===\n")
    
    try:
        # 初始化 Qdrant 数据库
        qdrant_db = QdrantVectorDatabase(
            api_url=settings.qdrant_api_url,
            database=settings.qdrant_database,
            pwd=settings.qdrant_pwd
        )
        
        print("1. 连接到 Qdrant...")
        if not qdrant_db.connect():
            print("❌ 连接失败，请确保 Qdrant 服务正在运行")
            return
        print("✅ 连接成功")
        
        collection_name = "example_collection"
        vector_dim = 128
        
        # 创建集合
        print(f"\n2. 创建集合 '{collection_name}'...")
        if qdrant_db.create_collection(collection_name, vector_dim, distance="cosine"):
            print("✅ 集合创建成功")
        else:
            print("❌ 集合创建失败")
            return
        
        # 生成示例向量数据
        print("\n3. 生成示例向量数据...")
        np.random.seed(42)
        vectors = [np.random.random(vector_dim).tolist() for _ in range(5)]
        ids = [f"doc_{i}" for i in range(5)]
        metadata = [{"text": f"这是第{i+1}个文档", "category": "example"} for i in range(5)]
        
        # 插入向量
        print("4. 插入向量数据...")
        if qdrant_db.insert_vectors(collection_name, vectors, ids, metadata):
            print("✅ 向量插入成功")
        else:
            print("❌ 向量插入失败")
            return
        
        # 搜索向量
        print("\n5. 执行向量搜索...")
        query_vector = np.random.random(vector_dim).tolist()
        search_params = {
            "with_payload": True,
            "score_threshold": 0.0
        }
        
        results = qdrant_db.search_by_vector(collection_name, query_vector, search_params, top_k=3)
        
        if results:
            print(f"✅ 搜索成功，找到 {len(results)} 个结果:")
            for i, result in enumerate(results):
                print(f"  {i+1}. ID: {result['id']}, Score: {result['score']:.4f}")
                print(f"     Text: {result['payload'].get('text', 'N/A')}")
        else:
            print("❌ 搜索失败")
        
        # 获取集合信息
        print("\n6. 获取集合信息...")
        info = qdrant_db.get_collection_info(collection_name)
        if info:
            print(f"✅ 集合信息:")
            print(f"   名称: {info.get('name')}")
            print(f"   向量数量: {info.get('vectors_count')}")
            print(f"   状态: {info.get('status')}")
        
        # 清理
        print("\n7. 清理测试数据...")
        if qdrant_db.delete_collection(collection_name):
            print("✅ 集合删除成功")
        
        qdrant_db.disconnect()
        print("✅ 连接已断开")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")

def example_milvus_usage():
    """Milvus 使用示例"""
    print("\n=== Milvus 向量数据库使用示例 ===\n")
    
    try:
        # 初始化 Milvus 数据库
        milvus_db = MilvusVectorDatabase(
            api_url=settings.milvus_api_url,
            database=settings.milvus_database,
            pwd=settings.milvus_pwd
        )
        
        print("1. 连接到 Milvus...")
        if not milvus_db.connect():
            print("❌ 连接失败，请确保 Milvus 服务正在运行")
            return
        print("✅ 连接成功")
        
        collection_name = "example_collection"
        vector_dim = 128
        
        # 创建集合
        print(f"\n2. 创建集合 '{collection_name}'...")
        metadata_fields = {
            "text": {"dtype": "VARCHAR", "max_length": 200}
        }
        
        if milvus_db.create_collection(collection_name, vector_dim, 
                                     auto_id=True, metadata_fields=metadata_fields):
            print("✅ 集合创建成功")
        else:
            print("❌ 集合创建失败")
            return
        
        # 生成示例向量数据
        print("\n3. 生成示例向量数据...")
        np.random.seed(42)
        vectors = [np.random.random(vector_dim).tolist() for _ in range(5)]
        metadata = [{"text": f"这是第{i+1}个文档"} for i in range(5)]
        
        # 插入向量
        print("4. 插入向量数据...")
        if milvus_db.insert_vectors(collection_name, vectors, metadata=metadata):
            print("✅ 向量插入成功")
        else:
            print("❌ 向量插入失败")
            return
        
        # 搜索向量
        print("\n5. 执行向量搜索...")
        query_vector = np.random.random(vector_dim).tolist()
        search_params = {
            "metric_type": "L2",
            "params": {"nprobe": 10},
            "output_fields": ["text"]
        }
        
        results = milvus_db.search_by_vector(collection_name, query_vector, search_params, top_k=3)
        
        if results:
            print(f"✅ 搜索成功，找到 {len(results)} 个结果:")
            for i, result in enumerate(results):
                print(f"  {i+1}. ID: {result['id']}, Distance: {result['score']:.4f}")
                print(f"     Entity: {result.get('entity', {})}")
        else:
            print("❌ 搜索失败")
        
        # 获取集合信息
        print("\n6. 获取集合信息...")
        info = milvus_db.get_collection_info(collection_name)
        if info:
            print(f"✅ 集合信息:")
            print(f"   名称: {info.get('name')}")
            print(f"   实体数量: {info.get('num_entities')}")
            print(f"   字段数量: {len(info.get('fields', []))}")
        
        # 清理
        print("\n7. 清理测试数据...")
        if milvus_db.delete_collection(collection_name):
            print("✅ 集合删除成功")
        
        milvus_db.disconnect()
        print("✅ 连接已断开")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")

def example_llm_usage():
    """LLM API 使用示例"""
    print("\n=== LLM API 使用示例 ===\n")
    
    try:
        # 验证设置
        settings.validate_model_settings()
        print("✅ LLM 配置验证通过")
        
        # 简单对话
        print("\n1. 简单对话示例...")
        response = call_llm(
            query="你好，请简单介绍一下自己",
            messages=[],
            tools=[],
            temperature=0.7
        )
        print(f"回复: {response}")
        
        # 带历史消息的对话
        print("\n2. 带历史消息的对话...")
        messages = [
            "你好",
            "你好！我是AI助手。",
            "请帮我解释一下什么是向量数据库"
        ]
        
        response = call_llm(
            query="它有什么优势？",
            messages=messages,
            tools=[],
            temperature=0.5
        )
        print(f"回复: {response}")
        
        # 工具调用示例
        print("\n3. 工具调用示例...")
        
        def calculate_sum(a: int, b: int) -> int:
            """计算两个数的和"""
            return a + b
        
        def get_weather(city: str) -> str:
            """获取天气信息（模拟）"""
            return f"{city}今天天气晴朗，温度25°C"
        
        tools = [
            Tool(
                name="calculate_sum",
                description="计算两个整数的和",
                func=calculate_sum,
                parameters={
                    "type": "object",
                    "properties": {
                        "a": {"type": "integer", "description": "第一个数"},
                        "b": {"type": "integer", "description": "第二个数"}
                    },
                    "required": ["a", "b"]
                }
            ),
            Tool(
                name="get_weather",
                description="获取指定城市的天气信息",
                func=get_weather,
                parameters={
                    "type": "object",
                    "properties": {
                        "city": {"type": "string", "description": "城市名称"}
                    },
                    "required": ["city"]
                }
            )
        ]
        
        response = call_llm(
            query="请帮我计算 15 + 27 的结果，然后查询北京的天气",
            messages=[],
            tools=tools,
            temperature=0.1,
            is_stream=False
        )
        
        print(f"回复: {response}")
        
        # 检查是否有工具调用
        import json
        if isinstance(response, str) and response.startswith('{'):
            try:
                result = json.loads(response)
                if 'tool_calls' in result:
                    print("🔧 模型想要调用工具:")
                    for tool_call in result['tool_calls']:
                        print(f"   - {tool_call['function']['name']}: {tool_call['function']['arguments']}")
            except json.JSONDecodeError:
                pass
        
    except ValueError as e:
        print(f"❌ 配置错误: {str(e)}")
        print("请检查 .env 文件中的 LLM 配置")
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 Dependencies 模块使用示例")
    print("=" * 50)
    
    # 检查基本配置
    try:
        print(f"Qdrant URL: {settings.qdrant_api_url}")
        print(f"Milvus URL: {settings.milvus_api_url}")
        print(f"Model API: {settings.model_api}")
        print()
    except Exception as e:
        print(f"❌ 配置加载失败: {str(e)}")
        print("请确保 .env 文件配置正确")
        return
    
    # 运行示例
    try:
        # 向量数据库示例（需要服务运行）
        print("注意: 向量数据库示例需要相应的服务正在运行")
        print("如果服务未运行，可以使用 'make start-minimal' 启动")
        print()
        
        # 可以选择性运行示例
        run_qdrant = input("是否运行 Qdrant 示例? (y/N): ").lower().startswith('y')
        if run_qdrant:
            example_qdrant_usage()
        
        run_milvus = input("是否运行 Milvus 示例? (y/N): ").lower().startswith('y')
        if run_milvus:
            example_milvus_usage()
        
        run_llm = input("是否运行 LLM API 示例? (y/N): ").lower().startswith('y')
        if run_llm:
            example_llm_usage()
        
        print("\n🎉 示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n\n👋 示例已取消")
    except Exception as e:
        print(f"\n❌ 运行失败: {str(e)}")

if __name__ == "__main__":
    main()
