from typing import List, Tuple, Optional, Dict, Union
from collections import defaultdict
from enum import Enum

class RelationType(Enum):
    DEPENDENCY = "dependency" # 依赖于，文件与文件之间的关系
    
    INCLUDE = "include" # 包括，类与类，类与方法，方法与方法之间的关系

    INHERIT = "inherit" # 继承，类之间的关系

    IMPLEMENT = "implement" # 完成，接口/抽象类 与 类之间的关系

    OVERWRITE = "overwrite" # 重写，方法之间的关系

    CALL = "call" # 调用，方法之间的关系

class CodeTreeNode:
    def __init__(self, code_snippet: str, line_start: int, line_end: int, **kwargs):
        self.code_snippet = code_snippet
        self.line_start = line_start
        self.line_end = line_end
        self.children: List['CodeTreeNode'] = []
        self.relations: Dict[RelationType, List[Union['CodeTreeNode', str]]] = defaultdict(list)
    
    def add_child(self, child: 'CodeTreeNode') -> None:
        """添加子节点"""
        self.children.append(child)
    
    def add_relation(self, relation_type: RelationType, target: Union['CodeTreeNode', str]) -> None:
        """添加关系"""
        self.relations[relation_type].append(target)
    
    def get_relations(self, relation_type: RelationType) -> List[Union['CodeTreeNode', str]]:
        """获取指定类型的关系"""
        return self.relations.get(relation_type, [])

class CodeTreeNodeFile(CodeTreeNode):
    def __init__(self, code_snippet: str, line_start: int, line_end: int, file_path: str, dependencies: Optional[List[Union['CodeTreeNodeFile', str]]] = None):
        super().__init__(code_snippet=code_snippet, line_start=line_start, line_end=line_end)
        self.file_path = file_path
        
        # 将dependencies添加到relations中
        if dependencies:
            self.relations[RelationType.DEPENDENCY] = dependencies

class CodeTreeNodeClass(CodeTreeNode):
    def __init__(self, code_snippet: str, line_start: int, line_end: int, name: str, properties: Optional[Dict[str, Union[str, 'CodeTreeNodeClass']]] = None):
        super().__init__(code_snippet, line_start, line_end)
        self.name = name
        self.properties = properties or {}

class CodeTreeNodeFunc(CodeTreeNode):
    def __init__(self, code_snippet: str, line_start: int, line_end: int, name: str, parameters: Optional[List[Tuple[str, Union[str, CodeTreeNodeClass]]]] = None, outputs: Optional[Union[str, CodeTreeNodeClass]] = None):
        super().__init__(code_snippet, line_start, line_end)
        self.name = name
        self.parameters = parameters or []
        self.outputs = outputs
