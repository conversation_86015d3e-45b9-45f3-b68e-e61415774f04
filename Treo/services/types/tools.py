from pydantic import BaseModel, Field
from typing import Callable, Any, Optional, Dict

class Tool(BaseModel):
    """工具定义类"""

    model_config = {"arbitrary_types_allowed": True}

    name: str = Field(..., description="The name of the tool")
    description: str = Field(..., description="The description of the tool")
    func: Callable[..., Any] = Field(..., description="The function to call when the tool is used")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Tool parameters schema for LLM")

# 增加一个装饰器，将函数转换为工具
def tool(func: Callable[..., Any]) -> Tool:
    # 读取func的parameters信息
    return Tool(
        name=func.__name__,
        description=func.__doc__ or "No description",
        func=func,
        parameters=func.parameters if hasattr(func, 'parameters') else None
    )
