version: '3.8'

services:
  # Qdrant 向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant_benchmark
    ports:
      - "6333:6333"  # REST API
      - "6334:6334"  # gRPC API
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Milvus 向量数据库 (简化版，单机部署)
  milvus:
    image: milvusdb/milvus:v2.3.3
    container_name: milvus_benchmark
    command: ["milvus", "run", "standalone"]
    ports:
      - "19530:19530"  # Milvus gRPC port
      - "9091:9091"    # Milvus metrics port
    volumes:
      - milvus_data:/var/lib/milvus
    environment:
      - ETCD_USE_EMBED=true
      - COMMON_STORAGETYPE=local
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      timeout: 20s
      retries: 5
      start_period: 90s

volumes:
  qdrant_data:
    driver: local
  milvus_data:
    driver: local

networks:
  default:
    name: benchmark_network
