# Settings 配置管理模块

这个模块提供了统一的环境变量配置管理功能，支持从 `.env` 文件加载配置并提供全局访问接口。

## 功能特性

- 🔧 自动加载 `.env` 文件中的环境变量
- 🏗️ 单例模式确保全局唯一配置实例
- ✅ 分类验证不同模块的必需配置
- 🔢 自动类型转换（如整数类型的环境变量）
- 📝 完整的类型注解支持

## 支持的配置项

### 百度翻译API配置
- `BAIDU_APP_ID`: 百度翻译应用ID
- `BAIDU_APP_KEY`: 百度翻译应用密钥
- `BAIDU_ENDPOINT`: 百度翻译API端点
- `BAIDU_TRANSLATE_PATH`: 百度翻译API路径

### JoyBuilder API配置
#### 模型相关
- `MODEL_APIKEY`: 模型API密钥
- `MODEL_API`: 模型API地址
- `MODEL`: 使用的模型名称

#### 嵌入模型相关
- `EMBEDDING_APIKEY`: 嵌入模型API密钥
- `EMBEDDING_API`: 嵌入模型API地址
- `EMBEDDING`: 使用的嵌入模型名称

### Milvus配置
- `MILVUS_API_URL`: Milvus服务地址
- `MILVUS_DATABASE`: Milvus数据库名称
- `MILVUS_PWD`: Milvus密码

### Qdrant配置
- `QDRANT_API_URL`: Qdrant服务地址
- `QDRANT_DATABASE`: Qdrant数据库名称
- `QDRANT_PWD`: Qdrant密码

### Chunk配置
- `CHUNK_TYPE`: 分块类型
- `CHUNK_WINDOW_SIZE`: 分块窗口大小（整数）
- `CHUNK_OVERFLOW_SIZE`: 分块溢出大小（整数）

## 使用方法

### 1. 基本使用

```python
from services.settings.settings import settings

# 直接使用全局设置实例
print(f"百度APP ID: {settings.baidu_app_id}")
print(f"模型API: {settings.model_api}")
print(f"Chunk窗口大小: {settings.chunk_window_size}")
```

### 2. 使用单例函数

```python
from services.settings.settings import get_settings

# 获取设置实例
config = get_settings()
print(f"Milvus URL: {config.milvus_api_url}")
```

### 3. 直接实例化（不推荐）

```python
from services.settings.settings import Settings

# 直接创建实例（会创建新的实例，不是单例）
config = Settings()
```

### 4. 配置验证

```python
from services.settings.settings import settings

try:
    # 验证百度翻译配置
    settings.validate_required_settings()
    print("百度翻译配置验证通过")
    
    # 验证模型配置
    settings.validate_model_settings()
    print("模型配置验证通过")
    
    # 验证嵌入模型配置
    settings.validate_embedding_settings()
    print("嵌入模型配置验证通过")
    
    # 验证Milvus配置
    settings.validate_milvus_settings()
    print("Milvus配置验证通过")
    
    # 验证Qdrant配置
    settings.validate_qdrant_settings()
    print("Qdrant配置验证通过")
    
    # 验证Chunk配置
    settings.validate_chunk_settings()
    print("Chunk配置验证通过")
    
except ValueError as e:
    print(f"配置验证失败: {e}")
```

## 实现细节

### 单例模式
Settings 类使用单例模式确保全局只有一个配置实例：

```python
# 全局设置实例
_settings = None

def get_settings() -> Settings:
    """获取全局设置实例（单例模式）"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings

# 便捷的全局设置实例
settings = get_settings()
```

### 类型转换
对于需要整数类型的环境变量，提供了自动转换功能：

```python
def _get_int_env(self, key: str) -> Optional[int]:
    """获取整数类型的环境变量"""
    value = os.getenv(key)
    if value is not None:
        try:
            return int(value)
        except ValueError:
            return None
    return None
```

### 分类验证
提供了针对不同模块的配置验证方法：
- `validate_required_settings()`: 验证百度翻译配置
- `validate_model_settings()`: 验证模型配置
- `validate_embedding_settings()`: 验证嵌入模型配置
- `validate_milvus_settings()`: 验证Milvus配置
- `validate_qdrant_settings()`: 验证Qdrant配置
- `validate_chunk_settings()`: 验证Chunk配置

## 注意事项

1. **环境变量文件**: 确保项目根目录下有 `.env` 文件
2. **类型安全**: 所有配置项都使用了 `Optional[type]` 类型注解
3. **错误处理**: 验证方法会抛出 `ValueError` 异常，需要适当处理
4. **单例模式**: 推荐使用 `settings` 全局实例或 `get_settings()` 函数

## 测试

运行测试脚本验证配置是否正常工作：

```bash
python test_settings.py
```

测试脚本会验证：
- 配置加载功能
- 单例模式
- 各种验证方法
- 所有配置项的显示
