import os
from dotenv import load_dotenv
from typing import Optional

class Settings:
    def __init__(self):
        """初始化设置类并加载环境变量"""
        self.load_env_variables()
    
    def load_env_variables(self):
        """加载环境变量到实例属性"""
        # 加载 .env 文件
        load_dotenv()

        # 百度翻译API配置
        self.baidu_app_id: str = os.getenv('BAIDU_APP_ID', '')
        self.baidu_app_key: str = os.getenv('BAIDU_APP_KEY', '')
        self.baidu_endpoint: str = os.getenv('BAIDU_ENDPOINT', '')
        self.baidu_translate_path: str = os.getenv('BAIDU_TRANSLATE_PATH', '')

        # JoyBuilder API配置 - 模型相关
        self.model_apikey: str = os.getenv('MODEL_APIKEY', '')
        self.model_api: str = os.getenv('MODEL_API', '')
        self.model: str = os.getenv('MODEL', '')

        # JoyBuilder API配置 - 嵌入模型相关
        self.embedding_apikey: str = os.getenv('EMBEDDING_APIKEY', '')
        self.embedding_api: str = os.getenv('EMBEDDING_API', '')
        self.embedding: str = os.getenv('EMBEDDING', '')

        # Milvus配置
        self.milvus_api_url: str = os.getenv('MILVUS_API_URL', '')
        self.milvus_database: str = os.getenv('MILVUS_DATABASE', '')
        self.milvus_pwd: str = os.getenv('MILVUS_PWD', '')

        # Qdrant配置
        self.qdrant_api_url: str = os.getenv('QDRANT_API_URL', '')
        self.qdrant_database: str = os.getenv('QDRANT_DATABASE', '')
        self.qdrant_pwd: str = os.getenv('QDRANT_PWD', '')

        # Chunk配置
        self.chunk_type: str = os.getenv('CHUNK_TYPE', '')
        self.chunk_window_size: Optional[int] = self._get_int_env('CHUNK_WINDOW_SIZE')
        self.chunk_overflow_size: Optional[int] = self._get_int_env('CHUNK_OVERFLOW_SIZE')

    def _get_int_env(self, key: str) -> Optional[int]:
        """获取整数类型的环境变量"""
        value = os.getenv(key)
        if value is not None:
            try:
                return int(value)
            except ValueError:
                return None
        return None
        
    def validate_required_settings(self) -> bool:
        """验证必需的环境变量是否已设置"""
        required_vars = [
            ('BAIDU_APP_ID', self.baidu_app_id),
            ('BAIDU_APP_KEY', self.baidu_app_key),
            ('BAIDU_ENDPOINT', self.baidu_endpoint),
            ('BAIDU_TRANSLATE_PATH', self.baidu_translate_path)
        ]

        missing_vars = [name for name, value in required_vars if not value]

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        return True

    def validate_model_settings(self) -> bool:
        """验证模型相关的环境变量是否已设置"""
        required_vars = [
            ('MODEL_APIKEY', self.model_apikey),
            ('MODEL_API', self.model_api),
            ('MODEL', self.model)
        ]

        missing_vars = [name for name, value in required_vars if not value]

        if missing_vars:
            raise ValueError(f"Missing required model environment variables: {', '.join(missing_vars)}")

        return True

    def validate_embedding_settings(self) -> bool:
        """验证嵌入模型相关的环境变量是否已设置"""
        required_vars = [
            ('EMBEDDING_APIKEY', self.embedding_apikey),
            ('EMBEDDING_API', self.embedding_api),
            ('EMBEDDING', self.embedding)
        ]

        missing_vars = [name for name, value in required_vars if not value]

        if missing_vars:
            raise ValueError(f"Missing required embedding environment variables: {', '.join(missing_vars)}")

        return True

    def validate_milvus_settings(self) -> bool:
        """验证Milvus相关的环境变量是否已设置"""
        required_vars = [
            ('MILVUS_API_URL', self.milvus_api_url),
            ('MILVUS_DATABASE', self.milvus_database),
            ('MILVUS_PWD', self.milvus_pwd)
        ]

        missing_vars = [name for name, value in required_vars if not value]

        if missing_vars:
            raise ValueError(f"Missing required Milvus environment variables: {', '.join(missing_vars)}")

        return True

    def validate_qdrant_settings(self) -> bool:
        """验证Qdrant相关的环境变量是否已设置"""
        required_vars = [
            ('QDRANT_API_URL', self.qdrant_api_url),
            ('QDRANT_DATABASE', self.qdrant_database),
            ('QDRANT_PWD', self.qdrant_pwd)
        ]

        missing_vars = [name for name, value in required_vars if not value]

        if missing_vars:
            raise ValueError(f"Missing required Qdrant environment variables: {', '.join(missing_vars)}")

        return True

    def validate_chunk_settings(self) -> bool:
        """验证Chunk相关的环境变量是否已设置"""
        required_vars = [
            ('CHUNK_TYPE', self.chunk_type),
            ('CHUNK_WINDOW_SIZE', self.chunk_window_size),
            ('CHUNK_OVERFLOW_SIZE', self.chunk_overflow_size)
        ]

        missing_vars = [name for name, value in required_vars if not value]

        if missing_vars:
            raise ValueError(f"Missing required Chunk environment variables: {', '.join(missing_vars)}")

        return True


# 全局设置实例
_settings = None

def get_settings() -> Settings:
    """获取全局设置实例（单例模式）"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings

# 便捷的全局设置实例
settings = get_settings()
