#!/usr/bin/env python3
"""
测试UUID生成功能，验证每次调用都能生成唯一的标识符
"""

import uuid

def test_uuid_generation():
    """测试UUID生成的唯一性"""
    print("Testing UUID generation for task identification...")
    
    # 生成多个UUID，验证它们都是唯一的
    uuids = []
    for i in range(10):
        task_id = str(uuid.uuid4())
        uuids.append(task_id)
        print(f"Generated task_id {i+1}: {task_id}")
    
    # 验证所有UUID都是唯一的
    unique_uuids = set(uuids)
    print(f"\nGenerated {len(uuids)} UUIDs, {len(unique_uuids)} unique")
    
    if len(uuids) == len(unique_uuids):
        print("✅ All UUIDs are unique - test passed!")
        return True
    else:
        print("❌ Some UUIDs are duplicated - test failed!")
        return False

if __name__ == "__main__":
    test_uuid_generation()
