#!/usr/bin/env python3
"""
TreeSitter功能演示脚本
展示如何使用TreeSitter解析Java和Python代码
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.treesitter.treesitter import TreeSitter
from services.treesitter.processors.processor_factory import ProcessorFactory, get_language_from_file_extension
from services.types.language import SuffixLanguage
from services.types.code_tree_node import CodeTreeNodeFile, CodeTreeNodeClass, CodeTreeNodeFunc


def print_tree_structure(node, indent=0):
    """递归打印代码树结构"""
    prefix = "  " * indent
    
    if isinstance(node, CodeTreeNodeFile):
        print(f"{prefix}📁 File: {node.file_path}")
    elif isinstance(node, CodeTreeNodeClass):
        print(f"{prefix}🏛️  Class: {node.name}")
    elif isinstance(node, CodeTreeNodeFunc):
        print(f"{prefix}⚙️  Function: {node.name}")
        if node.parameters:
            params = ", ".join([f"{name}: {type_}" for name, type_ in node.parameters])
            print(f"{prefix}   Parameters: {params}")
        if node.outputs:
            print(f"{prefix}   Returns: {node.outputs}")
    else:
        print(f"{prefix}📄 Node: {type(node).__name__}")
    
    # 打印关系信息
    if hasattr(node, 'relations') and node.relations:
        for relation_type, targets in node.relations.items():
            if targets:
                print(f"{prefix}   🔗 {relation_type.value}: {len(targets)} relations")
    
    # 递归打印子节点
    if hasattr(node, 'children'):
        for child in node.children:
            print_tree_structure(child, indent + 1)


def demo_processor_factory():
    """演示ProcessorFactory功能"""
    print("=== ProcessorFactory Demo ===")
    
    # 测试支持的语言
    print("Supported languages:")
    for lang in ProcessorFactory.get_supported_languages():
        print(f"  - {lang.value}")
    
    # 测试文件扩展名推断
    test_files = ["test.java", "example.py", "readme.txt"]
    print("\nFile extension inference:")
    for file in test_files:
        lang = get_language_from_file_extension(file)
        supported = ProcessorFactory.is_supported(lang)
        print(f"  {file} -> {lang.value} (supported: {supported})")
    
    print()


def demo_treesitter_parsing():
    """演示TreeSitter解析功能"""
    print("=== TreeSitter Parsing Demo ===")
    
    # 使用examples目录作为工作目录
    examples_dir = os.path.dirname(os.path.abspath(__file__))
    
    try:
        # 创建TreeSitter实例
        treesitter = TreeSitter(examples_dir)
        
        print(f"Working directory: {examples_dir}")
        
        # 扫描文件
        files = treesitter._scan_files()
        print(f"Found {len(files)} supported files:")
        for file in files:
            print(f"  - {os.path.relpath(file, examples_dir)}")
        
        print("\n--- Parsing individual files ---")
        
        # 解析单个文件
        for file_path in files:
            print(f"\nParsing: {os.path.basename(file_path)}")
            try:
                result = treesitter._parse_file(file_path)
                if result:
                    print_tree_structure(result)
                else:
                    print("  Failed to parse file")
            except Exception as e:
                print(f"  Error: {e}")
        
        print("\n--- Batch parsing ---")
        
        # 批量解析（使用较小的并发数以便观察）
        try:
            results = treesitter.batch_parse(concurrency=2)
            print(f"Successfully parsed {len(results)} files")
            
            for result in results:
                print(f"\n📁 {os.path.basename(result.file_path)}:")
                print_tree_structure(result, indent=1)
                
        except Exception as e:
            print(f"Batch parsing failed: {e}")
    
    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()


def demo_snippet_parsing():
    """演示代码片段解析功能"""
    print("\n=== Code Snippet Parsing Demo ===")
    
    examples_dir = os.path.dirname(os.path.abspath(__file__))
    treesitter = TreeSitter(examples_dir)
    
    # Java代码片段
    java_code = """
    public class HelloWorld {
        public static void main(String[] args) {
            System.out.println("Hello, World!");
        }
    }
    """
    
    print("Parsing Java snippet:")
    try:
        java_result = treesitter.parse_snippet("test.java", java_code, 1, 6)
        if java_result:
            print_tree_structure(java_result)
        else:
            print("  Failed to parse Java snippet")
    except Exception as e:
        print(f"  Error: {e}")
    
    # Python代码片段
    python_code = """
def greet(name: str) -> str:
    return f"Hello, {name}!"

class Greeter:
    def __init__(self, greeting: str):
        self.greeting = greeting
    
    def greet(self, name: str) -> str:
        return f"{self.greeting}, {name}!"
    """
    
    print("\nParsing Python snippet:")
    try:
        python_result = treesitter.parse_snippet("test.py", python_code, 1, 10)
        if python_result:
            print_tree_structure(python_result)
        else:
            print("  Failed to parse Python snippet")
    except Exception as e:
        print(f"  Error: {e}")


def main():
    """主函数"""
    print("🌳 TreeSitter Demo")
    print("=" * 50)
    
    try:
        demo_processor_factory()
        demo_treesitter_parsing()
        demo_snippet_parsing()
        
        print("\n✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
