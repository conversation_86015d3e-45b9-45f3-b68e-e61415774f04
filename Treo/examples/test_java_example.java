package com.example;

import java.util.List;
import java.util.ArrayList;

/**
 * 示例Java类，用于测试TreeSitter解析功能
 */
public class TestJavaExample {
    private String name;
    private int age;
    
    public TestJavaExample(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getAge() {
        return age;
    }
    
    public void setAge(int age) {
        this.age = age;
    }
    
    public void printInfo() {
        System.out.println("Name: " + getName() + ", Age: " + getAge());
    }
    
    public static void main(String[] args) {
        TestJavaExample example = new TestJavaExample("John", 25);
        example.printInfo();
    }
}

class SubClass extends TestJavaExample {
    private String email;
    
    public SubClass(String name, int age, String email) {
        super(name, age);
        this.email = email;
    }
    
    @Override
    public void printInfo() {
        super.printInfo();
        System.out.println("Email: " + email);
    }
}
