"""
示例Python模块，用于测试TreeSitter解析功能
"""

import os
import sys
from typing import List, Optional


class TestPythonExample:
    """测试Python类"""
    
    def __init__(self, name: str, age: int):
        self.name = name
        self.age = age
    
    def get_name(self) -> str:
        """获取姓名"""
        return self.name
    
    def set_name(self, name: str) -> None:
        """设置姓名"""
        self.name = name
    
    def get_age(self) -> int:
        """获取年龄"""
        return self.age
    
    def set_age(self, age: int) -> None:
        """设置年龄"""
        self.age = age
    
    def print_info(self) -> None:
        """打印信息"""
        print(f"Name: {self.get_name()}, Age: {self.get_age()}")


class SubClass(TestPythonExample):
    """子类示例"""
    
    def __init__(self, name: str, age: int, email: str):
        super().__init__(name, age)
        self.email = email
    
    def print_info(self) -> None:
        """重写打印信息方法"""
        super().print_info()
        print(f"Email: {self.email}")


def create_example(name: str, age: int) -> TestPythonExample:
    """创建示例对象的工厂函数"""
    return TestPythonExample(name, age)


def main():
    """主函数"""
    example = create_example("John", 25)
    example.print_info()
    
    sub_example = SubClass("Jane", 30, "<EMAIL>")
    sub_example.print_info()


if __name__ == "__main__":
    main()
