#!/usr/bin/env python3
"""
并发搜索示例
演示如何使用改进后的CoirJoycoderBenchmarkModel进行并发搜索
"""

import os
import sys
import time
from typing import Dict

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.codebase import CodeBaseApi
from benchmarks.coir.coir_joycoder_benchmark import CoirJoycoderBenchmarkModel
from utils.logger import logger


def create_sample_corpus() -> Dict[str, Dict[str, str]]:
    """创建示例语料库"""
    return {
        "doc1": {
            "title": "Python函数定义",
            "text": "def hello_world(): print('Hello, World!')",
            "language": "python"
        },
        "doc2": {
            "title": "JavaScript函数",
            "text": "function greet(name) { return `Hello, ${name}!`; }",
            "language": "javascript"
        },
        "doc3": {
            "title": "Java类定义",
            "text": "public class HelloWorld { public static void main(String[] args) { System.out.println(\"Hello World\"); } }",
            "language": "java"
        },
        "doc4": {
            "title": "C++程序",
            "text": "#include <iostream>\nint main() { std::cout << \"Hello World\" << std::endl; return 0; }",
            "language": "cpp"
        },
        "doc5": {
            "title": "Go程序",
            "text": "package main\nimport \"fmt\"\nfunc main() { fmt.Println(\"Hello, World!\") }",
            "language": "go"
        }
    }


def create_sample_queries() -> Dict[str, str]:
    """创建示例查询"""
    return {
        "query1": "如何打印Hello World",
        "query2": "函数定义的语法",
        "query3": "主函数的实现",
        "query4": "输出语句的使用",
        "query5": "程序入口点",
        "query6": "字符串输出方法",
        "query7": "控制台打印功能",
        "query8": "基本程序结构",
        "query9": "变量声明语法",
        "query10": "代码注释方式"
    }


def benchmark_search_performance():
    """基准测试搜索性能"""
    
    # 初始化API客户端
    base_api = CodeBaseApi({
        "api_url": "http://localhost:8080", 
        "timeout": 300
    })
    
    # 创建测试数据
    corpus = create_sample_corpus()
    queries = create_sample_queries()
    
    logger.info("=== 并发搜索性能测试 ===")
    
    # 测试不同的并发数设置
    concurrent_settings = [1, 2, 4, 8]
    
    for concurrent_workers in concurrent_settings:
        logger.info(f"\n--- 测试并发数: {concurrent_workers} ---")
        
        # 创建模型实例
        model = CoirJoycoderBenchmarkModel(
            base_service=base_api,
            output_dir="./test_results",
            tasks=["test-task"],  # 使用测试任务
            concurrent_workers=concurrent_workers
        )
        
        # 模拟设置项目ID（实际使用中需要先初始化数据集）
        # 这里仅作为示例，实际使用时需要调用 model.init_dataset()
        model.task_name = "test-task"
        # model.corpushash2projectid[CoirUtils.generate_corpus_hash(corpus)] = "test-project-id"
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 执行搜索（注意：这里会因为没有实际的项目ID而失败，仅作为示例）
            # results = model.search(corpus, queries, top_k=5)
            
            # 模拟搜索时间
            time.sleep(len(queries) / concurrent_workers * 0.1)  # 模拟每个查询0.1秒
            
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"并发数 {concurrent_workers}: 完成 {len(queries)} 个查询，耗时 {duration:.2f} 秒")
            logger.info(f"平均每个查询耗时: {duration/len(queries):.3f} 秒")
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")


def demonstrate_concurrent_usage():
    """演示并发使用方法"""
    
    logger.info("=== 并发搜索使用示例 ===")
    
    # 初始化API客户端
    base_api = CodeBaseApi({
        "api_url": "http://localhost:8080", 
        "timeout": 300
    })
    
    # 方法1: 在初始化时设置并发数
    logger.info("\n方法1: 初始化时设置并发数")
    model1 = CoirJoycoderBenchmarkModel(
        base_service=base_api,
        output_dir="./results/concurrent_test",
        tasks=["stackoverflow-qa"],
        concurrent_workers=6  # 设置6个并发工作线程
    )
    logger.info(f"模型默认并发数: {model1.concurrent_workers}")
    
    # 方法2: 在搜索时动态设置并发数
    logger.info("\n方法2: 搜索时动态设置并发数")
    model2 = CoirJoycoderBenchmarkModel(
        base_service=base_api,
        output_dir="./results/concurrent_test",
        tasks=["stackoverflow-qa"],
        concurrent_workers=4  # 默认4个并发
    )
    
    corpus = create_sample_corpus()
    queries = create_sample_queries()
    
    # 使用默认并发数
    logger.info(f"使用默认并发数 {model2.concurrent_workers}")
    # results1 = model2.search(corpus, queries, top_k=5)
    
    # 动态覆盖并发数
    logger.info("动态设置并发数为10")
    # results2 = model2.search(corpus, queries, top_k=5, concurrent_workers=10)
    
    logger.info("并发搜索配置完成！")


if __name__ == "__main__":
    # 设置日志级别
    import logging
    logger.setLevel(logging.INFO)
    
    print("并发搜索功能演示")
    print("=" * 50)
    
    # 演示使用方法
    demonstrate_concurrent_usage()
    
    # 性能基准测试（可选）
    # benchmark_search_performance()
    
    print("\n演示完成！")
    print("\n使用说明:")
    print("1. 在初始化CoirJoycoderBenchmarkModel时设置concurrent_workers参数")
    print("2. 或在调用search方法时通过kwargs传递concurrent_workers参数")
    print("3. 建议根据服务器性能和网络条件调整并发数")
    print("4. 过高的并发数可能导致服务器压力过大或请求超时")
