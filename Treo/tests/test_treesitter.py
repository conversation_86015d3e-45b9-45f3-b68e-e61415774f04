import unittest
import tempfile
import os
from unittest.mock import patch, MagicMock

from services.treesitter.treesitter import TreeSitter
from services.treesitter.processors.java_processor import JavaProcessor
from services.treesitter.processors.python_processor import PythonProcessor
from services.treesitter.processors.processor_factory import ProcessorFactory, get_language_from_file_extension
from services.types.language import SuffixLanguage
from services.types.code_tree_node import CodeTreeNode, CodeTreeNodeFile, CodeTreeNodeClass, CodeTreeNodeFunc, RelationType


class TestProcessorFactory(unittest.TestCase):
    """测试ProcessorFactory工厂类"""
    
    def test_get_processor_java(self):
        """测试获取Java处理器"""
        processor = ProcessorFactory.get_processor(SuffixLanguage.JAVA)
        self.assertEqual(processor, JavaProcessor)
    
    def test_get_processor_python(self):
        """测试获取Python处理器"""
        processor = ProcessorFactory.get_processor(SuffixLanguage.PYTHON)
        self.assertEqual(processor, PythonProcessor)
    
    def test_get_processor_unsupported(self):
        """测试获取不支持的语言处理器"""
        processor = ProcessorFactory.get_processor(SuffixLanguage.TEXT)
        self.assertIsNone(processor)
    
    def test_is_supported(self):
        """测试语言支持检查"""
        self.assertTrue(ProcessorFactory.is_supported(SuffixLanguage.JAVA))
        self.assertTrue(ProcessorFactory.is_supported(SuffixLanguage.PYTHON))
        self.assertFalse(ProcessorFactory.is_supported(SuffixLanguage.TEXT))
    
    def test_get_supported_languages(self):
        """测试获取支持的语言列表"""
        languages = ProcessorFactory.get_supported_languages()
        self.assertIn(SuffixLanguage.JAVA, languages)
        self.assertIn(SuffixLanguage.PYTHON, languages)
    
    def test_get_language_from_file_extension(self):
        """测试从文件扩展名推断语言"""
        self.assertEqual(get_language_from_file_extension("test.java"), SuffixLanguage.JAVA)
        self.assertEqual(get_language_from_file_extension("test.py"), SuffixLanguage.PYTHON)
        self.assertEqual(get_language_from_file_extension("test.txt"), SuffixLanguage.TEXT)


class TestJavaProcessor(unittest.TestCase):
    """测试JavaProcessor类"""
    
    def test_get_supported_language(self):
        """测试获取支持的语言类型"""
        self.assertEqual(JavaProcessor.get_supported_language(), SuffixLanguage.JAVA)
    
    @patch('tree_sitter.Node')
    def test_extract_code_snippet(self, mock_node):
        """测试提取代码片段"""
        mock_node.start_byte = 0
        mock_node.end_byte = 10
        content = "public class Test {}"
        
        result = JavaProcessor.extract_code_snippet(mock_node, content)
        self.assertEqual(result, "public cla")
    
    @patch('tree_sitter.Node')
    def test_get_line_numbers(self, mock_node):
        """测试获取行号范围"""
        mock_node.start_point = (0, 0)
        mock_node.end_point = (5, 10)
        
        start, end = JavaProcessor.get_line_numbers(mock_node)
        self.assertEqual(start, 1)
        self.assertEqual(end, 6)


class TestPythonProcessor(unittest.TestCase):
    """测试PythonProcessor类"""
    
    def test_get_supported_language(self):
        """测试获取支持的语言类型"""
        self.assertEqual(PythonProcessor.get_supported_language(), SuffixLanguage.PYTHON)
    
    @patch('tree_sitter.Node')
    def test_extract_code_snippet(self, mock_node):
        """测试提取代码片段"""
        mock_node.start_byte = 0
        mock_node.end_byte = 15
        content = "class TestClass:\n    pass"
        
        result = PythonProcessor.extract_code_snippet(mock_node, content)
        self.assertEqual(result, "class TestClass")


class TestTreeSitter(unittest.TestCase):
    """测试TreeSitter主类"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.treesitter = TreeSitter(self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.treesitter.workdir, os.path.abspath(self.temp_dir))
        self.assertEqual(self.treesitter.ignores, [])
        self.assertIsInstance(self.treesitter.parsed_files, dict)
        self.assertIsInstance(self.treesitter.file_hashes, dict)
    
    def test_init_with_ignores(self):
        """测试带忽略模式的初始化"""
        ignores = ["*.tmp", "test/*"]
        ts = TreeSitter(self.temp_dir, ignores)
        self.assertEqual(ts.ignores, ignores)
    
    def test_get_file_hash(self):
        """测试文件哈希计算"""
        content = "test content"
        hash1 = self.treesitter._get_file_hash("test.py", content)
        hash2 = self.treesitter._get_file_hash("test.py", content)
        hash3 = self.treesitter._get_file_hash("test.py", "different content")
        
        self.assertEqual(hash1, hash2)
        self.assertNotEqual(hash1, hash3)
    
    def test_is_file_changed(self):
        """测试文件变化检测"""
        file_path = "test.py"
        content1 = "content1"
        content2 = "content2"
        
        # 第一次检查，文件不存在于缓存中
        self.assertTrue(self.treesitter._is_file_changed(file_path, content1))
        
        # 添加到缓存
        self.treesitter.file_hashes[file_path] = self.treesitter._get_file_hash(file_path, content1)
        
        # 相同内容，未变化
        self.assertFalse(self.treesitter._is_file_changed(file_path, content1))
        
        # 不同内容，已变化
        self.assertTrue(self.treesitter._is_file_changed(file_path, content2))
    
    def test_should_ignore(self):
        """测试文件忽略检查"""
        ignore_patterns = ["*.tmp", "test/*", "__pycache__"]
        
        # 创建测试文件路径
        temp_file = os.path.join(self.temp_dir, "test.tmp")
        test_file = os.path.join(self.temp_dir, "test", "file.py")
        cache_file = os.path.join(self.temp_dir, "__pycache__", "test.pyc")
        normal_file = os.path.join(self.temp_dir, "normal.py")
        
        self.assertTrue(self.treesitter._should_ignore(temp_file, ignore_patterns))
        self.assertTrue(self.treesitter._should_ignore(test_file, ignore_patterns))
        self.assertTrue(self.treesitter._should_ignore(cache_file, ignore_patterns))
        self.assertFalse(self.treesitter._should_ignore(normal_file, ignore_patterns))
    
    def test_load_gitignore(self):
        """测试加载.gitignore文件"""
        # 创建.gitignore文件
        gitignore_path = os.path.join(self.temp_dir, '.gitignore')
        with open(gitignore_path, 'w') as f:
            f.write("*.tmp\n")
            f.write("# comment\n")
            f.write("test/\n")
            f.write("\n")  # 空行
        
        patterns = self.treesitter._load_gitignore()
        self.assertIn("*.tmp", patterns)
        self.assertIn("test/", patterns)
        self.assertNotIn("# comment", patterns)
        self.assertNotIn("", patterns)
    
    def test_scan_files(self):
        """测试文件扫描"""
        # 创建测试文件
        java_file = os.path.join(self.temp_dir, "Test.java")
        python_file = os.path.join(self.temp_dir, "test.py")
        text_file = os.path.join(self.temp_dir, "readme.txt")
        
        with open(java_file, 'w') as f:
            f.write("public class Test {}")
        with open(python_file, 'w') as f:
            f.write("class Test: pass")
        with open(text_file, 'w') as f:
            f.write("This is a text file")
        
        files = self.treesitter._scan_files()
        
        # 应该包含Java和Python文件，但不包含文本文件
        self.assertIn(java_file, files)
        self.assertIn(python_file, files)
        self.assertNotIn(text_file, files)


class TestCodeTreeNode(unittest.TestCase):
    """测试CodeTreeNode相关类"""
    
    def test_code_tree_node_creation(self):
        """测试CodeTreeNode创建"""
        node = CodeTreeNode("test code", 1, 10)
        self.assertEqual(node.code_snippet, "test code")
        self.assertEqual(node.line_start, 1)
        self.assertEqual(node.line_end, 10)
        self.assertEqual(len(node.children), 0)
        self.assertEqual(len(node.relations), 0)
    
    def test_add_child(self):
        """测试添加子节点"""
        parent = CodeTreeNode("parent", 1, 10)
        child = CodeTreeNode("child", 2, 5)
        
        parent.add_child(child)
        self.assertEqual(len(parent.children), 1)
        self.assertEqual(parent.children[0], child)
    
    def test_add_relation(self):
        """测试添加关系"""
        node1 = CodeTreeNode("node1", 1, 5)
        node2 = CodeTreeNode("node2", 6, 10)
        
        node1.add_relation(RelationType.CALL, node2)
        relations = node1.get_relations(RelationType.CALL)
        self.assertEqual(len(relations), 1)
        self.assertEqual(relations[0], node2)
    
    def test_code_tree_node_file(self):
        """测试CodeTreeNodeFile"""
        file_node = CodeTreeNodeFile("file content", 1, 100, "/path/to/file.py")
        self.assertEqual(file_node.file_path, "/path/to/file.py")
    
    def test_code_tree_node_class(self):
        """测试CodeTreeNodeClass"""
        class_node = CodeTreeNodeClass("class Test:", 1, 10, "Test")
        self.assertEqual(class_node.name, "Test")
        self.assertEqual(class_node.properties, {})
    
    def test_code_tree_node_func(self):
        """测试CodeTreeNodeFunc"""
        func_node = CodeTreeNodeFunc("def test():", 1, 5, "test")
        self.assertEqual(func_node.name, "test")
        self.assertEqual(func_node.parameters, [])
        self.assertIsNone(func_node.outputs)


if __name__ == '__main__':
    unittest.main()
