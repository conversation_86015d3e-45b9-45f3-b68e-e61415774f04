import hashlib
import xxhash
import random
import json
from typing import Dict, <PERSON>, Tu<PERSON>, Any
from pathlib import Path

try:
    import xxhash
    XXHASH_AVAILABLE = True
except ImportError:
    XXHASH_AVAILABLE = False

class CoirUtils:
    
    @staticmethod
    def get_project_id(project_name: str) -> str:
        """从本地路径获取项目ID"""
        return hashlib.sha256((project_name).encode('utf-8')).hexdigest()
    
    @staticmethod
    def generate_corpus_hash(corpus: Dict[str, Dict[str, str]]) -> str:
        """
        Generate a stable hash for the corpus data structure.

        This method addresses two key issues:
        1. Performance: Avoids creating large JSON strings for huge corpus data
        2. Stability: Uses cryptographic hash functions that produce consistent results

        Args:
            corpus: Dictionary mapping document IDs to document content

        Returns:
            Hexadecimal hash string that uniquely identifies the corpus content
        """
        if XXHASH_AVAILABLE:
            # Use xxhash for better performance
            hasher = xxhash.xxh64()
        else:
            # Fallback to SHA-256 for stability
            hasher = hashlib.sha256()

        # Sort document IDs to ensure consistent ordering
        sorted_doc_ids = sorted(corpus.keys())

        # Hash each document in a deterministic order
        for doc_id in sorted_doc_ids:
            doc = corpus[doc_id]

            # Hash document ID
            hasher.update(doc_id.encode('utf-8'))

            # Hash document fields in sorted order for consistency
            sorted_fields = sorted(doc.keys())
            for field in sorted_fields:
                field_value = doc[field]
                hasher.update(field.encode('utf-8'))
                hasher.update(field_value.encode('utf-8'))

        # Return hexadecimal digest
        return hasher.hexdigest()

    @staticmethod
    def sample_task_data(tasks: Dict[str, Tuple[Dict[str, Dict[str, str]], Dict[str, str], Dict[str, Dict[str, int]]]],
                        num_samples: int = 3) -> Dict[str, Dict[str, Any]]:
        """
        从各个任务中采样指定数量的query及其对应的前3个最佳corpus样本

        Args:
            tasks: 任务数据字典，格式为 {task_name: (corpus, queries, qrels)}
            num_samples: 每个任务采样的query数量

        Returns:
            采样后的数据，格式为 {task_name: {"samples": [{"query": {...}, "top_docs": [...]}], "description": "..."}}
        """
        sampled_data = {}

        # 任务描述映射
        task_descriptions = {
            "codetrans-dl": "代码翻译任务 - 将代码从一种编程语言翻译到另一种语言",
            "stackoverflow-qa": "Stack Overflow问答 - 基于编程问题找到相关的代码解决方案",
            "apps": "编程问题求解 - 根据问题描述找到对应的代码实现",
            "codefeedback-mt": "代码反馈（多轮）- 多轮对话中的代码改进建议",
            "codefeedback-st": "代码反馈（单轮）- 单轮对话中的代码改进建议",
            "codetrans-contest": "代码竞赛翻译 - 竞赛级别的代码翻译任务",
            "synthetic-text2sql": "文本到SQL - 将自然语言查询转换为SQL语句",
            "cosqa": "代码搜索问答 - 基于自然语言查询搜索相关代码",
            "codesearchnet": "代码搜索网络 - 大规模代码搜索任务",
            "codesearchnet-ccr": "代码搜索网络（CCR）- 跨语言代码检索任务"
        }

        for task_name, (corpus, queries, qrels) in tasks.items():
            # 随机采样指定数量的query
            query_ids = list(queries.keys())
            if len(query_ids) < num_samples:
                sampled_query_ids = query_ids
            else:
                sampled_query_ids = random.sample(query_ids, num_samples)

            task_samples = []

            for query_id in sampled_query_ids:
                query_text = queries[query_id]

                # 获取该query对应的相关文档，按分数排序取前3个
                if query_id in qrels:
                    # 按相关性分数降序排序，取前3个
                    sorted_docs = sorted(qrels[query_id].items(), key=lambda x: x[1], reverse=True)[:3]

                    top_docs = []
                    for doc_id, score in sorted_docs:
                        doc = corpus.get(doc_id, {})
                        top_docs.append({
                            "id": doc_id,
                            "title": doc.get("title", ""),
                            "text": doc.get("text", ""),
                            "language": doc.get("language", ""),
                            "score": score
                        })

                    task_samples.append({
                        "query": {
                            "id": query_id,
                            "text": query_text
                        },
                        "top_docs": top_docs
                    })

            sampled_data[task_name] = {
                "samples": task_samples,
                "description": task_descriptions.get(task_name, "代码信息检索任务"),
                "total_queries": len(queries),
                "total_corpus": len(corpus)
            }

        return sampled_data

    @staticmethod
    def generate_data_visualization_html(sampled_data: Dict[str, Dict[str, Any]], output_path: str = "coir_data_visualization.html"):
        """
        生成数据可视化HTML文件 - 新的卡片式布局

        Args:
            sampled_data: 采样后的数据
            output_path: 输出HTML文件路径
        """
        html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COIR 数据集可视化</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .task-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .task-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .task-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }

        .task-card.active {
            border-color: #667eea;
            transform: translateY(-5px);
        }

        .task-card h3 {
            color: #333;
            font-size: 1.3em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .task-card p {
            color: #666;
            font-size: 0.95em;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .task-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.8em;
            color: #888;
            margin-top: 2px;
        }

        .samples-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            overflow: hidden;
            display: none;
        }

        .samples-container.active {
            display: block;
        }

        .samples-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .samples-header h2 {
            font-size: 1.8em;
            font-weight: 300;
        }

        .sample-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .sample-row:last-child {
            border-bottom: none;
        }

        .code-block {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-header {
            background: #2d3748;
            color: white;
            padding: 12px 16px;
            font-size: 0.9em;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .query-header {
            background: #e67e22;
        }

        .rank-1 { background: #27ae60; }
        .rank-2 { background: #3498db; }
        .rank-3 { background: #9b59b6; }

        .copy-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            transition: background 0.2s;
        }

        .copy-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .code-content {
            max-height: 300px;
            overflow-y: auto;
        }

        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0;
            font-size: 0.85em;
        }

        .score-badge {
            background: rgba(255,255,255,0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 8px;
        }

        .no-task-selected {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-task-selected h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
            color: #999;
        }

        @media (max-width: 768px) {
            .sample-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .task-cards {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>COIR 数据集可视化</h1>
            <p>代码信息检索基准测试数据集 - 交互式样本展示</p>
        </div>

        <!-- 任务卡片区域 -->
        <div class="task-cards">"""

        # 添加任务卡片
        for task_name, task_data in sampled_data.items():
            samples = task_data["samples"]
            description = task_data["description"]
            total_queries = task_data["total_queries"]
            total_corpus = task_data["total_corpus"]

            html_content += f"""
            <div class="task-card" onclick="showTaskSamples('{task_name}')">
                <h3>{task_name}</h3>
                <p>{description}</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">{len(samples)}</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">{total_queries}</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">{total_corpus}</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>"""

        html_content += """
        </div>

        <!-- 样本展示区域 -->
        <div class="samples-container" id="samples-container">
            <div class="no-task-selected">
                <h3>请选择一个任务</h3>
                <p>点击上方的任务卡片来查看对应的样本数据</p>
            </div>
        </div>
    </div>



    <script>
        // 存储所有任务数据"""

        # 添加任务数据到JavaScript（使用JSON编码避免注入）
        import json

        # 将数据转换为JSON格式
        js_data = {}
        for task_name, task_data in sampled_data.items():
            js_data[task_name] = {
                'name': task_name,
                'description': task_data["description"],
                'samples': []
            }

            for sample in task_data["samples"]:
                query = sample["query"]
                top_docs = sample["top_docs"]

                # 限制文本长度并清理特殊字符，避免过长内容导致问题
                def truncate_and_clean_text(text, max_length=300):
                    if not text:
                        return "无内容"
                    # 清理可能导致问题的字符
                    cleaned = text.replace('\r\n', '\n').replace('\r', '\n')
                    # # 限制长度
                    # if len(cleaned) > max_length:
                    #     return cleaned[:max_length] + "...\n[内容已截断]"
                    return cleaned

                js_data[task_name]['samples'].append({
                    'query': {
                        'id': query["id"],
                        'text': truncate_and_clean_text(query["text"])
                    },
                    'top_docs': [
                        {
                            'id': doc["id"],
                            'title': doc.get("title", ""),
                            'text': truncate_and_clean_text(doc.get("text", "")),
                            'language': doc.get("language", ""),
                            'score': doc.get("score", 0)
                        }
                        for doc in top_docs
                    ]
                })

        # 安全地将数据嵌入JavaScript
        html_content += f"""
        const taskData = {json.dumps(js_data, ensure_ascii=False, indent=8)};"""

        html_content += """

        // 显示任务样本
        function showTaskSamples(taskName) {
            console.log('Showing samples for task:', taskName);

            // 更新卡片状态
            document.querySelectorAll('.task-card').forEach(card => {
                card.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            const task = taskData[taskName];
            const container = document.getElementById('samples-container');

            if (!task) {
                console.error('Task not found:', taskName);
                return;
            }

            // 创建DOM元素而不是字符串拼接
            const samplesDiv = document.createElement('div');

            // 添加标题
            const headerDiv = document.createElement('div');
            headerDiv.className = 'samples-header';
            headerDiv.innerHTML = '<h2>' + escapeHtml(task.name) + '</h2><p>' + escapeHtml(task.description) + '</p>';
            samplesDiv.appendChild(headerDiv);

            // 添加样本
            task.samples.forEach((sample, index) => {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'sample-row';

                // 查询块
                const queryBlock = createCodeBlock(
                    '🔍 查询 ' + (index + 1),
                    sample.query.text,
                    'text',
                    'query-header'
                );
                rowDiv.appendChild(queryBlock);

                // 文档块
                sample.top_docs.forEach((doc, docIndex) => {
                    const docBlock = createCodeBlock(
                        '📄 排名 ' + (docIndex + 1) + ' (' + (doc.language || 'text') + ') - 分数: ' + doc.score,
                        doc.text,
                        doc.language || 'text',
                        'rank-' + (docIndex + 1)
                    );
                    rowDiv.appendChild(docBlock);
                });

                samplesDiv.appendChild(rowDiv);
            });

            container.innerHTML = '';
            container.appendChild(samplesDiv);
            container.classList.add('active');

            console.log('任务样本显示完成');
        }

        // 创建代码块元素
        function createCodeBlock(title, content, language, headerClass) {
            const blockDiv = document.createElement('div');
            blockDiv.className = 'code-block';

            const headerDiv = document.createElement('div');
            headerDiv.className = 'code-header ' + headerClass;

            const titleSpan = document.createElement('span');
            titleSpan.textContent = title;
            headerDiv.appendChild(titleSpan);

            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-btn';
            copyBtn.textContent = '复制';
            copyBtn.onclick = function() { copyText(this, content); };
            headerDiv.appendChild(copyBtn);

            const contentDiv = document.createElement('div');
            contentDiv.className = 'code-content';

            const pre = document.createElement('pre');
            pre.style.whiteSpace = 'pre-wrap';
            pre.style.wordWrap = 'break-word';
            pre.style.backgroundColor = '#f8f9fa';
            pre.style.padding = '10px';
            pre.style.borderRadius = '4px';
            pre.style.fontSize = '0.9em';
            pre.style.fontFamily = 'Monaco, Consolas, "Courier New", monospace';
            pre.textContent = content;
            contentDiv.appendChild(pre);

            blockDiv.appendChild(headerDiv);
            blockDiv.appendChild(contentDiv);

            return blockDiv;
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 复制文本
        function copyText(button, text) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = 'rgba(40, 167, 69, 0.8)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(255,255,255,0.2)';
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }
    </script>
</body>
</html>"""

        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return output_path

    @staticmethod
    def create_data_visualization(tasks: List[str] = None, num_samples: int = 3, output_path: str = "coir_data_visualization.html"):
        """
        创建COIR数据集可视化HTML文件的完整流程

        Args:
            tasks: 要处理的任务列表，如果为None则使用默认任务
            num_samples: 每个任务采样的query数量
            output_path: 输出HTML文件路径

        Returns:
            生成的HTML文件路径
        """
        # 导入必要的模块
        try:
            from coir.data_loader import get_tasks
        except ImportError:
            raise ImportError("无法导入coir.data_loader，请确保已安装coir包")

        # 使用默认任务列表
        if tasks is None:
            tasks = ["codetrans-dl", "stackoverflow-qa", "apps", "codefeedback-mt",
                    "codefeedback-st", "codetrans-contest", "synthetic-text2sql",
                    "cosqa", "codesearchnet", "codesearchnet-ccr"]

        print(f"🔄 正在加载 {len(tasks)} 个任务的数据...")

        # 加载任务数据
        task_data = get_tasks(tasks)

        if not task_data:
            raise ValueError("未能加载任何任务数据")

        print(f"✅ 成功加载 {len(task_data)} 个任务")

        # 采样数据
        print(f"🎯 正在从每个任务中采样 {num_samples} 个查询...")
        sampled_data = CoirUtils.sample_task_data(task_data, num_samples)

        # 生成HTML
        print(f"🎨 正在生成HTML可视化文件...")
        output_file = CoirUtils.generate_data_visualization_html(sampled_data, output_path)

        print(f"🎉 HTML文件已生成: {output_file}")

        # 打印统计信息
        total_samples = sum(len(data["samples"]) for data in sampled_data.values())
        total_queries = sum(data["total_queries"] for data in sampled_data.values())
        total_corpus = sum(data["total_corpus"] for data in sampled_data.values())

        print(f"📊 统计信息:")
        print(f"   - 任务数量: {len(sampled_data)}")
        print(f"   - 采样数量: {total_samples}")
        print(f"   - 总查询数: {total_queries}")
        print(f"   - 总文档数: {total_corpus}")

        return output_file