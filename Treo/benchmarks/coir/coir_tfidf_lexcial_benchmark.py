import os
import json
import rich
from typing import Dict, List
from coir.data_loader import get_tasks
from coir.beir.retrieval.search import BaseSearch
from coir.beir.retrieval.evaluation import EvaluateRetrieval
import rich.progress

from benchmarks.benchmark_model import BenchmarkModel
from benchmarks.coir.coir_utils import CoirUtils
from services.lexcial.lexcial_tfidf import LexicalTFIDF
from utils.logger import logger

DEFAULT_CACHE_DIR = './cache/tfidf_lexcial'

class CoirTFIDFLexicalBenchmarkModel(BenchmarkModel, BaseSearch):
    def __init__(self, base_service: LexicalTFIDF, output_dir: str, cache_dir: str = DEFAULT_CACHE_DIR, tasks: List[str] = ["codetrans-dl","stackoverflow-qa","apps","codefeedback-mt","codefeedback-st","cosqa","codesearchnet","codesearchnet-ccr"]):
        super().__init__(base_service, output_dir)
        self.base_service = base_service
        self.tasks = get_tasks(tasks=tasks)
        # 存储corpus hash到project id的映射
        self.corpushash2projectid = {}
        # 存储task name到临时目录的映射
        self.task_dirs = {}
        # 当前任务名称
        self.task_name = None

        self.cache_dir = cache_dir

    def init_dataset(self):
        """
        初始化数据集：
        1. 读取所有tasks的corpus数据
        2. 将每个文档样本的text保存为文件，文件名为doc_id，后缀根据language字段确定
        3. 为每个task创建项目并加载文件
        """
        logger.info("⏱️ Starting dataset initialization...")

        for task_name, task_data in self.tasks.items():
            logger.info(f"Initializing dataset for {task_name}...")

            corpus, _, _ = task_data
            
            # 记录映射关系
            corpus_hash = CoirUtils.generate_corpus_hash(corpus)
            
            # 创建BM25项目
            project_id = CoirUtils.get_project_id(task_name)

            self.corpushash2projectid[corpus_hash] = project_id

            # 创建临时目录存储文档文件
            task_dir = os.path.join(self.cache_dir, f"tfidf_{task_name}")
            self.task_dirs[task_name] = task_dir

            if os.path.exists(task_dir):
                self.base_service.load_context_files(task_dir, self.cache_dir, project_id)
                logger.info(f"Temporary directory for {task_name} already exists. Skipping creation.")
                continue

            os.makedirs(task_dir, exist_ok=True)

            # 将corpus中的每个文档保存为文件
            for doc_id, doc_content in corpus.items():
                # 确定文件扩展名
                language = doc_content.get('language', '')
                if language:
                    file_extension = f".{language}"
                else:
                    file_extension = ".txt"

                # 构建文件路径
                file_path = os.path.join(task_dir, f"{doc_id}{file_extension}")

                # 准备文件内容（合并title和text）
                content_parts = []
                if 'title' in doc_content and doc_content['title']:
                    content_parts.append(doc_content['title'])
                if 'text' in doc_content and doc_content['text']:
                    content_parts.append(doc_content['text'])

                file_content = '\n'.join(content_parts)

                # 写入文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(file_content)

            # 加载上下文文件
            result = self.base_service.load_context_files(task_dir, self.cache_dir, project_id)
            logger.info(f"Loaded {result.get('documents', 0)} documents for {task_name}")

        logger.info("✅ Dataset initialization completed")
    
    def search(self, corpus: Dict[str, Dict[str, str]], queries: Dict[str, str], top_k: int, *args, **kwargs) -> Dict[str, Dict[str, float]]:
        """
        执行搜索：
        1. 计算corpus的hash，获取对应的project_id
        2. 对每个query执行检索
        3. 从文件路径解析出doc_id并返回结果
        """
        # 计算corpus的hash，获取其对应的project_id
        corpus_hash = CoirUtils.generate_corpus_hash(corpus)
        project_id = self.corpushash2projectid[corpus_hash]
        self.base_service.load_context_files(self.task_dirs[self.task_name], self.cache_dir, project_id)

        # 存储所有查询的结果
        results = {}

        # 针对每个query，执行base_service.retrieve方法，获取结果
        for query_id, query_text in rich.progress.track(queries.items(), description="Query ..."):
            # 执行检索
            retrieve_results = self.base_service.retrieve(project_id, query_text, top_k=top_k)

            # # 从路径中解析出doc_id并构建结果
            query_results = {}
            for file_path, score in retrieve_results.items():
                # 从文件路径中提取doc_id（去掉扩展名）
                doc_id = os.path.splitext(os.path.basename(file_path))[0]
                query_results[doc_id] = score

            results[query_id] = query_results

        return retrieve_results


    def evaluate(self, top_k: int = 5, k_values: List[int] = [1, 3, 5, 10], max_query_count: int = 2000, *args, **kwargs) -> Dict[str, float]:
        results = {}

        retriever = EvaluateRetrieval(self, k_values=k_values)

        logger.info("Starting evaluation...")
        for task_name, task_data in self.tasks.items():
            output_file = os.path.join(self.output_dir, f"{task_name}.json")

            # Check if the output file already exists
            if os.path.exists(output_file):
                print(f"Results for {task_name} already exist. Skipping task.")
                continue
  
            corpus, queries, qrels = task_data
            self.task_name = task_name

            if len(queries) > max_query_count:
                new_queries = {}
                i = 0
                for k, v in queries.items():
                    if i >= max_query_count:
                        break
                    new_queries[k] = v
                    i += 1
                queries = new_queries

                
            # Retrieve results
            task_results = retriever.retrieve(corpus, queries)
            
            # Evaluate results
            ndcg, map, recall, precision = retriever.evaluate(qrels, task_results, retriever.k_values)
            metrics = {
                "NDCG": ndcg,
                "MAP": map,
                "Recall": recall,
                "Precision": precision
            }

            # Save results
            os.makedirs(self.output_dir, exist_ok=True)
            with open(output_file, 'w') as json_file:
                json.dump({"metrics": metrics}, json_file, indent=4)

            logger.info(f"Results for {task_name} saved to {self.output_dir}")
            results[task_name] = metrics

        # 计算results中所有指标的平均值，保存在本地文件中
        total_metrics = {
            "NDCG": {},
            "MAP": {},
            "Recall": {},
            "Precision": {}
        }
        for task_name, task_metrics in results.items():
            for metric_name, metric_value in task_metrics.items():
                if metric_name not in total_metrics:
                    total_metrics[metric_name] = {}
                if not total_metrics[metric_name]:
                    total_metrics[metric_name] = metric_value
                else:
                    for k, v in metric_value.items():
                        if k in total_metrics[metric_name]:
                            total_metrics[metric_name][k] += v
                

        for metric_name in total_metrics:
            for k in total_metrics[metric_name]:
                total_metrics[metric_name][k] /= len(results)

        output_file = os.path.join(self.output_dir, f"total.json")
        with open(output_file, 'w') as json_file:
            json.dump({"metrics": total_metrics}, json_file, indent=2, ensure_ascii=False)

        return results


