
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COIR 数据集可视化</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .task-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .task-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .task-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }

        .task-card.active {
            border-color: #667eea;
            transform: translateY(-5px);
        }

        .task-card h3 {
            color: #333;
            font-size: 1.3em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .task-card p {
            color: #666;
            font-size: 0.95em;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .task-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.8em;
            color: #888;
            margin-top: 2px;
        }

        .samples-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            overflow: hidden;
            display: none;
        }

        .samples-container.active {
            display: block;
        }

        .samples-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .samples-header h2 {
            font-size: 1.8em;
            font-weight: 300;
        }

        .sample-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .sample-row:last-child {
            border-bottom: none;
        }

        .code-block {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-header {
            background: #2d3748;
            color: white;
            padding: 12px 16px;
            font-size: 0.9em;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .query-header {
            background: #e67e22;
        }

        .rank-1 { background: #27ae60; }
        .rank-2 { background: #3498db; }
        .rank-3 { background: #9b59b6; }

        .copy-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            transition: background 0.2s;
        }

        .copy-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .code-content {
            max-height: 300px;
            overflow-y: auto;
        }

        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0;
            font-size: 0.85em;
        }

        .score-badge {
            background: rgba(255,255,255,0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 8px;
        }

        .no-task-selected {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-task-selected h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
            color: #999;
        }

        @media (max-width: 768px) {
            .sample-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .task-cards {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>COIR 数据集可视化</h1>
            <p>代码信息检索基准测试数据集 - 交互式样本展示</p>
        </div>

        <!-- 任务卡片区域 -->
        <div class="task-cards">
            <div class="task-card" onclick="showTaskSamples('codetrans-dl')">
                <h3>codetrans-dl</h3>
                <p>代码翻译任务 - 将代码从一种编程语言翻译到另一种语言</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">180</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">816</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('stackoverflow-qa')">
                <h3>stackoverflow-qa</h3>
                <p>Stack Overflow问答 - 基于编程问题找到相关的代码解决方案</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">1994</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">19931</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('apps')">
                <h3>apps</h3>
                <p>编程问题求解 - 根据问题描述找到对应的代码实现</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">3765</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">8765</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('codefeedback-mt')">
                <h3>codefeedback-mt</h3>
                <p>代码反馈（多轮）- 多轮对话中的代码改进建议</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">13277</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">66383</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('codefeedback-st')">
                <h3>codefeedback-st</h3>
                <p>代码反馈（单轮）- 单轮对话中的代码改进建议</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">31306</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">156526</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('codetrans-contest')">
                <h3>codetrans-contest</h3>
                <p>代码竞赛翻译 - 竞赛级别的代码翻译任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">221</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">1008</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('synthetic-text2sql')">
                <h3>synthetic-text2sql</h3>
                <p>文本到SQL - 将自然语言查询转换为SQL语句</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">5851</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">105851</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('cosqa')">
                <h3>cosqa</h3>
                <p>代码搜索问答 - 基于自然语言查询搜索相关代码</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">500</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">20604</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-go')">
                <h3>CodeSearchNet-go</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">8122</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">182440</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-java')">
                <h3>CodeSearchNet-java</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">10955</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">180866</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-javascript')">
                <h3>CodeSearchNet-javascript</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">3291</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">64854</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-ruby')">
                <h3>CodeSearchNet-ruby</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">1261</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">27570</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-python')">
                <h3>CodeSearchNet-python</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">14918</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">280310</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-php')">
                <h3>CodeSearchNet-php</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">14014</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">267725</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-ccr-go')">
                <h3>CodeSearchNet-ccr-go</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">8122</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">182735</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-ccr-java')">
                <h3>CodeSearchNet-ccr-java</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">10955</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">181061</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-ccr-javascript')">
                <h3>CodeSearchNet-ccr-javascript</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">3291</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">65201</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-ccr-ruby')">
                <h3>CodeSearchNet-ccr-ruby</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">1261</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">27588</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-ccr-python')">
                <h3>CodeSearchNet-ccr-python</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">14918</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">280652</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
            <div class="task-card" onclick="showTaskSamples('CodeSearchNet-ccr-php')">
                <h3>CodeSearchNet-ccr-php</h3>
                <p>代码信息检索任务</p>
                <div class="task-stats">
                    <div class="stat">
                        <div class="stat-number">3</div>
                        <div class="stat-label">样本数量</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">14014</div>
                        <div class="stat-label">总查询数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">268237</div>
                        <div class="stat-label">总文档数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 样本展示区域 -->
        <div class="samples-container" id="samples-container">
            <div class="no-task-selected">
                <h3>请选择一个任务</h3>
                <p>点击上方的任务卡片来查看对应的样本数据</p>
            </div>
        </div>
    </div>



    <script>
        // 存储所有任务数据
        const taskData = {
        "codetrans-dl": {
                "name": "codetrans-dl",
                "description": "代码翻译任务 - 将代码从一种编程语言翻译到另一种语言",
                "samples": [
                        {
                                "query": {
                                        "id": "740",
                                        "text": "%matplotlib inline\nimport math\nimport tensorflow as tf\nfrom tensorflow.keras.callbacks import LearningRateScheduler\nfrom d2l import tensorflow as d2l\ndef net():\n    return tf.keras.models.Sequential([\n        tf.keras.layers.Conv2D(filters=6, kernel_size=5, activation='relu', padding='same'),\n        tf.keras.layers.AvgPool2D(pool_size=2, strides=2),\n        tf.keras.layers.Conv2D(filters=16, kernel_size=5, activation='relu'),\n        tf.keras.layers.AvgPool2D(pool_size=2, strides=2),\n        tf.keras.layers.Flatten(),\n        tf.keras.layers.Dense(120, activation='relu'),\n        tf.keras.layers.Dense(84, activation='sigmoid'),\n        tf.keras.layers.Dense(10)])\nbatch_size = 256\ntrain_iter, test_iter = d2l.load_data_fashion_mnist(batch_size=batch_size)\ndef train(net_fn, train_iter, test_iter, num_epochs, lr, device=d2l.try_gpu(), custom_callback = False):\n    device_name = device._device_name\n    strategy = tf.distribute.OneDeviceStrategy(device_name)\n    with strategy.scope():\n        optimizer = tf.keras.optimizers.SGD(learning_rate=lr)\n        loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)\n        net = net_fn()\n        net.compile(optimizer=optimizer, loss=loss, metrics=['accuracy'])\n    callback = d2l.TrainCallback(net, train_iter, test_iter, num_epochs, device_name)\n    if custom_callback is False:\n        net.fit(train_iter, epochs=num_epochs, verbose=0, callbacks=[callback])\n    else:\n        net.fit(train_iter, epochs=num_epochs, verbose=0, callbacks=[callback, custom_callback])\n    return net\nlr, num_epochs = 0.3, 30\ntrain(net, train_iter, test_iter, num_epochs, lr)\nlr = 0.1\ndummy_model = tf.keras.models.Sequential([tf.keras.layers.Dense(10)])\ndummy_model.compile(tf.keras.optimizers.SGD(learning_rate=lr), loss='mse')\nscheduler = SquareRootScheduler(lr=0.1)\nd2l.plot(tf.range(num_epochs), [scheduler(t) for t in range(num_epochs)])\ntrain(net, train_iter, test_iter, num_epochs, lr, custom_callback=LearningRateScheduler(scheduler))\nclass FactorScheduler:\n    def __init__(self, factor=1, stop_factor_lr=1e-7, base_lr=0.1):\n        self.factor = factor\n        self.stop_factor_lr = stop_factor_lr\n        self.base_lr = base_lr\n    def __call__(self, num_update):\n        self.base_lr = max(self.stop_factor_lr, self.base_lr * self.factor)\n        return self.base_lr\nscheduler = FactorScheduler(factor=0.9, stop_factor_lr=1e-2, base_lr=2.0)\nd2l.plot(tf.range(50), [scheduler(t) for t in range(50)])\nclass MultiFactorScheduler:\n    def __init__(self, step, factor, base_lr):\n        self.step = step\n        self.factor = factor\n        self.base_lr = base_lr\n    def __call__(self, epoch):\n        if epoch in self.step:\n            self.base_lr = self.base_lr * self.factor\n            return self.base_lr\n        else:\n            return self.base_lr\nscheduler = MultiFactorScheduler(step=[15, 30], factor=0.5, base_lr=0.5)\nd2l.plot(tf.range(num_epochs), [scheduler(t) for t in range(num_epochs)])\ntrain(net, train_iter, test_iter, num_epochs, lr, custom_callback=LearningRateScheduler(scheduler))\nscheduler = CosineScheduler(max_update=20, base_lr=0.3, final_lr=0.01)\nd2l.plot(tf.range(num_epochs), [scheduler(t) for t in range(num_epochs)])\ntrain(net, train_iter, test_iter, num_epochs, lr, custom_callback=LearningRateScheduler(scheduler))\nscheduler = CosineScheduler(20, warmup_steps=5, base_lr=0.3, final_lr=0.01)\nd2l.plot(tf.range(num_epochs), [scheduler(t) for t in range(num_epochs)])\ntrain(net, train_iter, test_iter, num_epochs, lr, custom_callback=LearningRateScheduler(scheduler))"
                                },
                                "top_docs": [
                                        {
                                                "id": "c740",
                                                "title": "",
                                                "text": "%matplotlib inline\nimport warnings\nfrom d2l import paddle as d2l\nwarnings.filterwarnings(\"ignore\")\nimport math\nimport paddle\nfrom paddle import nn\nfrom paddle.optimizer import lr as lr_scheduler\ndef net_fn():\n    model = nn.Sequential(\n        nn.Conv2D(1, 6, kernel_size=5, padding=2), nn.ReLU(),\n        nn.MaxPool2D(kernel_size=2, stride=2),\n        nn.Conv2D(6, 16, kernel_size=5), nn.ReLU(),\n        nn.MaxPool2D(kernel_size=2, stride=2),\n        nn.Flatten(),\n        nn.Linear(16 * 5 * 5, 120), nn.ReLU(),\n        nn.Linear(120, 84), nn.ReLU(),\n        nn.Linear(84, 10))\n    return model\nloss = nn.CrossEntropyLoss()\ndevice = d2l.try_gpu()\nbatch_size = 256\ntrain_iter, test_iter = d2l.load_data_fashion_mnist(batch_size=batch_size)\ndef train(net, train_iter, test_iter, num_epochs, loss, trainer, device, scheduler=None):\n    animator = d2l.Animator(xlabel='epoch', xlim=[0, num_epochs], legend=['train loss', 'train acc', 'test acc'])\n    for epoch in range(num_epochs):\n        metric = d2l.Accumulator(3)\n        for i, (X, y) in enumerate(train_iter):\n            net.train()\n            trainer.clear_grad()\n            y_hat = net(X)\n            l = loss(y_hat, y)\n            l.backward()\n            trainer.step()\n            with paddle.no_grad():\n                metric.add(l * X.shape[0], d2l.accuracy(y_hat,y), X.shape[0])\n            train_loss = metric[0] / metric[2]\n            train_acc = metric[1] / metric[2]\n            if (i + 1) % 50 == 0:\n                animator.add(epoch + i / len(train_iter), (train_loss, train_acc, None))\n        test_acc = d2l.evaluate_accuracy_gpu(net, test_iter)\n        animator.add(epoch+1, (None, None, test_acc))\n        if scheduler:\n            if scheduler.__module__ == lr_scheduler.__name__:\n                scheduler.step()\n            else:\n                trainer.set_lr(scheduler(epoch))\n    print(f'train loss {train_loss:.3f}, train acc {train_acc:.3f}, f'test acc {test_acc:.3f}')\nlr, num_epochs = 0.3, 30\nnet = net_fn()\ntrainer = paddle.optimizer.SGD(learning_rate=lr, parameters=net.parameters())\ntrain(net, train_iter, test_iter, num_epochs, loss, trainer, device)\nlr = 0.1\ntrainer.set_lr(lr)\nscheduler = SquareRootScheduler(lr=0.1)\nd2l.plot(paddle.arange(num_epochs), [scheduler(t) for t in range(num_epochs)])\nnet = net_fn()\ntrainer = paddle.optimizer.SGD(learning_rate=lr , parameters=net.parameters())\ntrain(net, train_iter, test_iter, num_epochs, loss, trainer, device, scheduler)\nclass FactorScheduler:\n    def __init__(self, factor=1, stop_factor_lr=1e-7, base_lr=0.1):\n        self.factor = factor\n        self.stop_factor_lr = stop_factor_lr\n        self.base_lr = base_lr\n    def __call__(self, num_update):\n        self.base_lr = max(self.stop_factor_lr, self.base_lr * self.factor)\n        return self.base_lr\nscheduler = FactorScheduler(factor=0.9, stop_factor_lr=1e-2, base_lr=2.0)\nd2l.plot(paddle.arange(50), [scheduler(t) for t in range(50)])\nnet = net_fn()\nscheduler =paddle.optimizer.lr.MultiStepDecay(learning_rate=0.5, milestones=[15,30], gamma=0.5)\ntrainer = paddle.optimizer.SGD(learning_rate=scheduler, parameters=net.parameters())\ndef get_lr(trainer, scheduler):\n    lr=trainer.state_dict()['LR_Scheduler']['last_lr']\n    trainer.step()\n    scheduler.step()\n    return lr\nd2l.plot(paddle.arange(num_epochs), [get_lr(trainer, scheduler) for t in range(num_epochs)])\ntrain(net, train_iter, test_iter, num_epochs, loss, trainer, device, scheduler)\nscheduler = CosineScheduler(max_update=20, base_lr=0.3, final_lr=0.01)\nd2l.plot(paddle.arange(num_epochs), [scheduler(t) for t in range(num_epochs)])\nnet = net_fn()\ntrainer = paddle.optimizer.SGD(learning_rate=0.3, parameters=net.parameters())\ntrain(net, train_iter, test_iter, num_epochs, loss, trainer, device, scheduler)\nscheduler = CosineScheduler(20, warmup_steps=5, base_lr=0.3, final_lr=0.01)\nd2l.plot(paddle.arange(num_epochs), [scheduler(t) for t in range(num_epochs)])\nnet = net_fn()\ntrainer = paddle.optimizer.SGD(learning_rate=0.3, parameters=net.parameters())\ntrain(net, train_iter, test_iter, num_epochs, loss, trainer, device, scheduler)",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "651",
                                        "text": "import tensorflow as tf\nfrom tensorflow.keras.layers import Dense\nfrom d2l import tensorflow as d2l\ndef get_net():\n    net = tf.keras.Sequential()\n    net.add(Dense(256, input_shape = (512,), activation = \"relu\"))\n    net.add(Dense(128, activation = \"relu\"))\n    net.add(Dense(2, activation = \"linear\"))\n    return net\nx = tf.random.normal([1,512])\nnet = get_net()\nnet(x)\nnet = tf.function(net)\nnet(x)"
                                },
                                "top_docs": [
                                        {
                                                "id": "c651",
                                                "title": "",
                                                "text": "import warnings\nfrom d2l import paddle as d2l\nwarnings.filterwarnings(\"ignore\")\nimport paddle\nfrom paddle import nn\nfrom paddle.jit import to_static\nfrom paddle.static import InputSpec\ndef get_net():\n    blocks = [\n        nn.Linear(512, 256),\n        nn.ReLU(),\n        nn.Linear(256, 128),\n        nn.ReLU(),\n        nn.Linear(128, 2)\n    ]\n    net = nn.Sequential(*blocks)\n    return net\nx = paddle.randn((1, 512))\nnet = get_net()\nnet(x)\nnet = paddle.jit.to_static(net)\nnet(x)",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "715",
                                        "text": "%matplotlib inline\nimport numpy as np\nimport tensorflow as tf\nfrom mpl_toolkits import mplot3d\nfrom d2l import tensorflow as d2l\ndef f(x):\n    return x * tf.cos(np.pi * x)\ndef g(x):\n    return f(x) + 0.2 * tf.cos(5 * np.pi * x)\ndef annotate(text, xy, xytext):\n    d2l.plt.gca().annotate(text, xy=xy, xytext=xytext, arrowprops=dict(arrowstyle='->'))\nx = tf.range(0.5, 1.5, 0.01)\nd2l.set_figsize((4.5, 2.5))\nd2l.plot(x, [f(x), g(x)], 'x', 'risk')\nannotate('min of\\nempirical risk', (1.0, -1.2), (0.5, -1.1))\nannotate('min of risk', (1.1, -1.05), (0.95, -0.5))\nx = tf.range(-1.0, 2.0, 0.01)\nd2l.plot(x, [f(x), ], 'x', 'f(x)')\nannotate('local minimum', (-0.3, -0.25), (-0.77, -1.0))\nannotate('global minimum', (1.1, -0.95), (0.6, 0.8))\nx = tf.range(-2.0, 2.0, 0.01)\nd2l.plot(x, [x**3], 'x', 'f(x)')\nannotate('saddle point', (0, -0.2), (-0.52, -5.0))\nx, y = tf.meshgrid(tf.linspace(-1.0, 1.0, 101), tf.linspace(-1.0, 1.0, 101))\nz = x**2 - y**2\nax = d2l.plt.figure().add_subplot(111, projection='3d')\nax.plot_wireframe(x, y, z, **{'rstride': 10, 'cstride': 10})\nax.plot([0], [0], [0], 'rx')\nticks = [-1, 0, 1]\nd2l.plt.xticks(ticks)\nd2l.plt.yticks(ticks)\nax.set_zticks(ticks)\nd2l.plt.xlabel('x')\nd2l.plt.ylabel('y');\nx = tf.range(-2.0, 5.0, 0.01)\nd2l.plot(x, [tf.tanh(x)], 'x', 'f(x)')\nannotate('vanishing gradient', (4, 1), (2, 0.0))"
                                },
                                "top_docs": [
                                        {
                                                "id": "c715",
                                                "title": "",
                                                "text": "%matplotlib inline\nfrom mpl_toolkits import mplot3d\nfrom mxnet import np, npx\nfrom d2l import mxnet as d2l\nnpx.set_np()\ndef f(x):\n    return x * np.cos(np.pi * x)\ndef g(x):\n    return f(x) + 0.2 * np.cos(5 * np.pi * x)\ndef annotate(text, xy, xytext):\n    d2l.plt.gca().annotate(text, xy=xy, xytext=xytext, arrowprops=dict(arrowstyle='->'))\nx = np.arange(0.5, 1.5, 0.01)\nd2l.set_figsize((4.5, 2.5))\nd2l.plot(x, [f(x), g(x)], 'x', 'risk')\nannotate('min of\\nempirical risk', (1.0, -1.2), (0.5, -1.1))\nannotate('min of risk', (1.1, -1.05), (0.95, -0.5))\nx = np.arange(-1.0, 2.0, 0.01)\nd2l.plot(x, [f(x), ], 'x', 'f(x)')\nannotate('local minimum', (-0.3, -0.25), (-0.77, -1.0))\nannotate('global minimum', (1.1, -0.95), (0.6, 0.8))\nx = np.arange(-2.0, 2.0, 0.01)\nd2l.plot(x, [x**3], 'x', 'f(x)')\nannotate('saddle point', (0, -0.2), (-0.52, -5.0))\nx, y = np.meshgrid(np.linspace(-1.0, 1.0, 101), np.linspace(-1.0, 1.0, 101))\nz = x**2 - y**2\nax = d2l.plt.figure().add_subplot(111, projection='3d')\nax.plot_wireframe(x.asnumpy(), y.asnumpy(), z.asnumpy(), **{'rstride': 10, 'cstride': 10})\nax.plot([0], [0], [0], 'rx')\nticks = [-1, 0, 1]\nd2l.plt.xticks(ticks)\nd2l.plt.yticks(ticks)\nax.set_zticks(ticks)\nd2l.plt.xlabel('x')\nd2l.plt.ylabel('y');\nx = np.arange(-2.0, 5.0, 0.01)\nd2l.plot(x, [np.tanh(x)], 'x', 'f(x)')\nannotate('vanishing gradient', (4, 1), (2, 0.0))",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "stackoverflow-qa": {
                "name": "stackoverflow-qa",
                "description": "Stack Overflow问答 - 基于编程问题找到相关的代码解决方案",
                "samples": [
                        {
                                "query": {
                                        "id": "q19736",
                                        "text": "Set the selectedIndex property and selectedItems is empty I have an wpf application that use the MVVM with a datagrid. I set the selectedIndex property in the viewModel, but the SelectedItems property is empty. Shouldn't it have the selected the selected item?"
                                },
                                "top_docs": [
                                        {
                                                "id": "d19736",
                                                "title": "",
                                                "text": "Not necessarily. \nOne way of doing this is to set SelectedItem property in the datagrid xaml to a property on your view model which implements INotifyPropertyChanged. \nThen set the xaml binding mode to two way.\nThen if you click on a selected item it will trigger a change from the xaml binding to update the value in the view model",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q18644",
                                        "text": "javascript function internal scope property What is the Difference in internal scope property assigned on function declaration and on entering the function execution context?\nDefinition:\n[[Scope]] property is already written and stored in function object. [[Scope]] in contrast with Scope (Scope chain) is the property of a function instead of a context.\nLink:(http://dmitrysoshnikov.com/ecmascript/chapter-4-scope-chain/#function-creation)\nWhat i mean is :as soon as function gets declared will it be assigned scope property or during execution time will scope property gets assigned."
                                },
                                "top_docs": [
                                        {
                                                "id": "d18644",
                                                "title": "",
                                                "text": "This is the closure concept.  It is worded differently here than normal.  Basically there are two things going on -- first you have the closure, that is the variables that are declared locally to the context of the function definition are made available to the function.  This is the \"scope chain\" he refers to.  In addition, locally defined variables (var statements within the function) don't exist until the function starts at \"execution context\".  (Typically these are stored on the stack or a heap).",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q19465",
                                        "text": "Creating menu for NSSegmentedControl using XIB How do we create menu for a particular segment using xib for NSSegmentedControl ?"
                                },
                                "top_docs": [
                                        {
                                                "id": "d19465",
                                                "title": "",
                                                "text": "You just drag an NSMenu from the object library and release it over the segment you want it to be attached to.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "apps": {
                "name": "apps",
                "description": "编程问题求解 - 根据问题描述找到对应的代码实现",
                "samples": [
                        {
                                "query": {
                                        "id": "q5090",
                                        "text": "You are given an integer N. Consider all possible segments on the coordinate axis with endpoints at integer points with coordinates between 0 and N, inclusive; there will be $\\frac{n(n + 1)}{2}$ of them.\n\nYou want to draw these segments in several layers so that in each layer the segments don't overlap (they might touch at the endpoints though). You can not move the segments to a different location on the coordinate axis. \n\nFind the minimal number of layers you have to use for the given N.\n\n\n-----Input-----\n\nThe only input line contains a single integer N (1 ≤ N ≤ 100).\n\n\n-----Output-----\n\nOutput a single integer - the minimal number of layers required to draw the segments for the given N.\n\n\n-----Examples-----\nInput\n2\n\nOutput\n2\n\nInput\n3\n\nOutput\n4\n\nInput\n4\n\nOutput\n6\n\n\n\n-----Note-----\n\nAs an example, here are the segments and their optimal arrangement into layers for N = 4. [Image]"
                                },
                                "top_docs": [
                                        {
                                                "id": "d5090",
                                                "title": "",
                                                "text": "n=int(input())\nprint(max((i+1)*(n-i)for i in range(n)))\n",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q6198",
                                        "text": "Game \"Minesweeper 1D\" is played on a line of squares, the line's height is 1 square, the line's width is n squares. Some of the squares contain bombs. If a square doesn't contain a bomb, then it contains a number from 0 to 2 — the total number of bombs in adjacent squares.\n\nFor example, the correct field to play looks like that: 001*2***101*. The cells that are marked with \"*\" contain bombs. Note that on the correct field the numbers represent the number of bombs in adjacent cells. For example, field 2* is not correct, because cell with value 2 must have two adjacent cells with bombs.\n\nValera wants to make a correct field to play \"Minesweeper 1D\". He has already painted a squared field with width of n cells, put several bombs on the field and wrote numbers into some cells. Now he wonders how many ways to fill the remaining cells with bombs and numbers are there if we should get a correct field in the end.\n\n\n-----Input-----\n\nThe first line contains sequence of characters without spaces s_1s_2... s_{n} (1 ≤ n ≤ 10^6), containing only characters \"*\", \"?\" and digits \"0\", \"1\" or \"2\". If character s_{i} equals \"*\", then the i-th cell of the field contains a bomb. If character s_{i} equals \"?\", then Valera hasn't yet decided what to put in the i-th cell. Character s_{i}, that is equal to a digit, represents the digit written in the i-th square.\n\n\n-----Output-----\n\nPrint a single integer — the number of ways Valera can fill the empty cells and get a correct field.\n\nAs the answer can be rather large, print it modulo 1000000007 (10^9 + 7).\n\n\n-----Examples-----\nInput\n?01???\n\nOutput\n4\n\nInput\n?\n\nOutput\n2\n\nInput\n**12\n\nOutput\n0\n\nInput\n1\n\nOutput\n0\n\n\n\n-----Note-----\n\nIn the first test sample you can get the following correct fields: 001**1, 001***, 001*2*, 001*10."
                                },
                                "top_docs": [
                                        {
                                                "id": "d6198",
                                                "title": "",
                                                "text": "Mod=1000000007\ns=input()\nn=len(s)\na,b,c,d=1,0,0,0\nfor i in range(0,n):\n    if s[i]=='*':\n        a,b,c,d=0,(a+b+d)%Mod,0,0\n    elif s[i]=='?':\n        a,b,c,d=(a+b+c)%Mod,(a+b+d)%Mod,0,0\n    elif s[i]=='0':\n        a,b,c,d=0,0,(a+c)%Mod,0\n    elif s[i]=='1':\n        a,b,c,d=0,0,b,(a+c)%Mod\n    else:\n        a,b,c,d=0,0,0,(b+d)%Mod\nprint((a+b+c)%Mod)\n",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q7008",
                                        "text": "You are given a graph with $n$ nodes and $m$ directed edges. One lowercase letter is assigned to each node. We define a path's value as the number of the most frequently occurring letter. For example, if letters on a path are \"abaca\", then the value of that path is $3$. Your task is find a path whose value is the largest.\n\n\n-----Input-----\n\nThe first line contains two positive integers $n, m$ ($1 \\leq n, m \\leq 300\\,000$), denoting that the graph has $n$ nodes and $m$ directed edges.\n\nThe second line contains a string $s$ with only lowercase English letters. The $i$-th character is the letter assigned to the $i$-th node.\n\nThen $m$ lines follow. Each line contains two integers $x, y$ ($1 \\leq x, y \\leq n$), describing a directed edge from $x$ to $y$. Note that $x$ can be equal to $y$ and there can be multiple edges between $x$ and $y$. Also the graph can be not connected.\n\n\n-----Output-----\n\nOutput a single line with a single integer denoting the largest value. If the value can be arbitrarily large, output -1 instead.\n\n\n-----Examples-----\nInput\n5 4\nabaca\n1 2\n1 3\n3 4\n4 5\n\nOutput\n3\n\nInput\n6 6\nxzyabc\n1 2\n3 1\n2 3\n5 4\n4 3\n6 4\n\nOutput\n-1\n\nInput\n10 14\nxzyzyzyzqx\n1 2\n2 4\n3 5\n4 5\n2 6\n6 8\n6 5\n2 10\n3 9\n10 9\n4 6\n1 10\n2 8\n3 7\n\nOutput\n4\n\n\n\n-----Note-----\n\nIn the first sample, the path with largest value is $1 \\to 3 \\to 4 \\to 5$. The value is $3$ because the letter 'a' appears $3$ times."
                                },
                                "top_docs": [
                                        {
                                                "id": "d7008",
                                                "title": "",
                                                "text": "# NOT MY CODE\n# https://codeforces.com/contest/919/submission/80857731\n \nfrom types import GeneratorType\n \ndef bootstrap(f, stack=[]):\n    def wrappedfunc(*args, **kwargs):\n        to = f(*args, **kwargs)\n        if stack:\n            return to\n        else:\n            while True:\n                if type(to) is GeneratorType:\n                    stack.append(to)\n                    to = next(to)\n                else:\n                    stack.pop()\n                    if not stack:\n                        return to\n                    to = stack[-1].send(to)\n    return wrappedfunc\n \nimport sys\ninput=sys.stdin.readline\nfrom collections import defaultdict as dd\n'''\ndef iscyclic(g):\n    for i in range(1,n+1):\n        if(\n'''\nn,m=list(map(int,input().split()))\ns=input()\nd=dd(list)\nlol=0\nfor i in range(m):\n    u,v=list(map(int,input().split()))\n    if(u==v):\n        lol=1\n        print(-1)\n        return\n    d[u].append(v)\n'''\nif(iscyclic(d)):\n    lol=1\n    print(-1)\n    return\n'''\nvis=[0]*(n+1)\nrvis=[0]*(n+1)\ncou=[0]\ndp=[dd(int) for i in range(n+1)]\nmx=[0]\nh=[0]\n#print(d)\n@bootstrap\ndef dfs(u):\n    if(h[0]==1):\n        yield\n    vis[u]=1\n    rvis[u]=1\n    for i in d[u]:\n        if(h[0]==1):\n            yield\n        if(vis[i]==0):\n            yield dfs(i)\n            if(h[0]==1):\n                yield            \n        elif(rvis[i]==True):\n            h[0]=1\n            yield\n    rvis[u]=0\n    if(h[0]==1):\n        yield\n    for i in d[u]:\n        for j in dp[i]:\n            dp[u][j]=max(dp[u][j],dp[i][j])\n            mx[0]=max(mx[0],dp[u][j])\n    dp[u][s[u-1]]+=1\n    #print(u,dp[u])\n    mx[0]=max(mx[0],dp[u][s[u-1]])\n    yield\n#print(dp)\nfor i in range(1,n+1):\n    if(vis[i]==0):\n        cou=[0]\n        dfs(i)\nif(h[0]):\n    print(-1)\nelse:\n    print(mx[0])\n",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "codefeedback-mt": {
                "name": "codefeedback-mt",
                "description": "代码反馈（多轮）- 多轮对话中的代码改进建议",
                "samples": [
                        {
                                "query": {
                                        "id": "q54486",
                                        "text": "'user': Develop a program that splits a given paragraph into sentences., 'assistant': Sure, we can do this by using the Natural Language Toolkit (nltk) in Python. The nltk.sent_tokenize() function can be used to split text into sentences.\n\nHere's a plan:\n1. Import nltk library\n2. Define the paragraph text\n3. Use nltk.sent_tokenize() function to split the text into sentences\n\nLet's write and execute this Python code.\n```python\nimport nltk\n\nparagraph = \"Python is a fantastic language. It's simple to understand and write. It is widely used for various applications, from web development to data science. It has a huge community to support!\"\n\n# split the paragraph into sentences\nsentences = nltk.sent_tokenize(paragraph)\nsentences\n```, 'user': Execution result: \n['Python is a fantastic language.',\n \"It's simple to understand and write.\",\n 'It is widely used for various applications, from web development to data science.',\n 'It has a huge community to support!'], 'assistant': The paragraph has been successfully split into sentences. Here they are:\n\n1. 'Python is a fantastic language.'\n2. 'It's simple to understand and write.'\n3. 'It is widely used for various applications, from web development to data science.'\n4. 'It has a huge community to support!', 'user': The code works well and is formatted correctly. However, it would be helpful to add comments to explain what each line does."
                                },
                                "top_docs": [
                                        {
                                                "id": "c54486",
                                                "title": "",
                                                "text": "Of course! I understand the importance of code readability and explanations. Here's the enhanced version of it with comments:\n\n```python\n# Import the Natural Language Toolkit (nltk) library.\nimport nltk\n\n# Defining the paragraph.\nparagraph = \"Python is a fantastic language. It's simple to understand and write. It is widely used for various applications, from web development to data science. It has a huge community to support!\"\n\n# The sent_tokenize() function from the nltk library is used to split the paragraph into sentences.\nsentences = nltk.sent_tokenize(paragraph)\nsentences\n```\n\nThese comments describe the role and functionality of each line in the Python code.\n\nIf necessary, I can re-run this commented code. Just let me know!",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q65742",
                                        "text": "'user': You are tasked with implementing a custom PyTorch Lightning module for a linear model with a specific structure. The provided code snippet outlines the beginning of the module, but it requires further completion. Your task is to extend the `LinearModel` class to include the forward pass and training logic.\n\nYour completed `LinearModel` class should:\n- Inherit from `nn.Module`.\n- Have an `__init__` method that initializes the input dimension (`input_dim`) and the number of classes (`num_classes`).\n- Include a `forward` method that takes input data and passes it through a linear layer, followed by a softmax activation function.\n- Implement a training step method that computes the loss and accuracy, and returns the loss for optimization.\n\nYou should use the provided `nn.Flatten` and `nn.Linear` layers to build the model.\n\nYour task is to complete the `LinearModel` class by adding the required methods to achieve the specified functionality., 'assistant': To complete the `LinearModel` class, you need to define the `__init__`, `forward`, and `training_step` methods. Here's the completed code:\n\n```python\nimport torch\nimport torch.nn as nn\n\nclass LinearModel(nn.Module):\n    def __init__(self, input_dim, num_classes):\n        super(LinearModel, self).__init__()\n        self.flatten = nn.Flatten()\n        self.linear = nn.Linear(input_dim, num_classes)\n        self.softmax = nn.Softmax(dim=1)\n\n    def forward(self, x):\n        x = self.flatten(x)\n        x = self.linear(x)\n        x = self.softmax(x)\n        return x\n\n    def training_step(self, batch):\n        x, y = batch\n        out = self.forward(x)\n        loss = nn.CrossEntropyLoss()(out, y)\n        preds = torch.argmax(out, dim=1)\n        acc = torch.tensor(torch.sum(preds == y).item() / len(preds))\n        return {'loss': loss, 'acc': acc}\n```\n\nIn the `__init__` method, we initialize the necessary layers, including `nn.Flatten` to flatten the input data, `nn.Linear` as the linear layer, and `nn.Softmax` for the softmax activation function.\n\nThe `forward` method takes an input tensor `x`, flattens it using `nn.Flatten`, passes it through the linear layer, and applies the softmax activation function. The output is returned.\n\nThe `training_step` method takes a batch of data and performs the forward pass using the `forward` method. It then calculates the loss using `nn.CrossEntropyLoss` and computes the accuracy by comparing the predicted labels with the true labels. The loss and accuracy are returned as a dictionary.\n\nYou can now use this completed `LinearModel` class to build and train linear models in PyTorch Lightning., 'user': The forward method can be made more efficient by removing the softmax activation function. Softmax is not necessary in the forward method because nn.CrossEntropyLoss already applies it internally., 'assistant': You're correct. In practice, the softmax activation function is often omitted during the forward pass in PyTorch implementations if `nn.CrossEntropyLoss` is being used as the loss function in training since this loss function already applies softmax internally for computation. \n\nHere's the updated code:\n\n```python\nimport torch\nimport torch.nn as nn\n\nclass LinearModel(nn.Module):\n    def __init__(self, input_dim, num_classes):\n        super(LinearModel, self).__init__()\n        self.flatten = nn.Flatten()\n        self.linear = nn.Linear(input_dim, num_classes)\n\n    def forward(self, x):\n        x = self.flatten(x)\n        x = self.linear(x)\n        return x\n\n    def training_step(self, batch):\n        x, y = batch\n        out = self.forward(x)\n        loss = nn.CrossEntropyLoss()(out, y)\n        preds = torch.argmax(out, dim=1)\n        acc = torch.tensor(torch.sum(preds == y).item() / len(preds))\n        return {'loss': loss, 'acc': acc}\n```\n\nWith this setup, the `forward` method passes the raw output of the linear layer to `nn.CrossEntropyLoss()` in the `training_step` method, which then applies the softmax activation function as part of its computation., 'user': Consider adding comments to the code for better understanding and maintainability."
                                },
                                "top_docs": [
                                        {
                                                "id": "c65742",
                                                "title": "",
                                                "text": "Sure, let's add some comments to the code to help clarify what each part of it's doing:\n\n```python\nimport torch\nimport torch.nn as nn\n\nclass LinearModel(nn.Module):\n    def __init__(self, input_dim, num_classes):\n        super(LinearModel, self).__init__()\n        # Flatten layer to convert input into 1D tensor\n        self.flatten = nn.Flatten()\n        # Linear layer that uses the input dimensions and the number of classes\n        self.linear = nn.Linear(input_dim, num_classes)\n\n    def forward(self, x):\n        # Flatten the input\n        x = self.flatten(x)\n        # Pass the input through the linear layer. \n        # No Softmax used here because nn.CrossEntropyLoss applies it internally\n        x = self.linear(x)\n        return x\n\n    def training_step(self, batch):\n        # Split the batch into data (x) and labels(y)\n        x, y = batch\n        # Forward pass\n        out = self.forward(x)\n        # Compute loss\n        loss = nn.CrossEntropyLoss()(out, y)\n        # Predict the classes by picking the output with the highest value\n        preds = torch.argmax(out, dim=1)\n        # Compute accuracy\n        acc = torch.tensor(torch.sum(preds == y).item() / len(preds))\n        # Return loss and accuracy\n        return {'loss': loss, 'acc': acc}\n```\n\nThese comments should give a better understanding of the LinearModel class and its methods. The LinearModel class implements a linear model with a specified structure in PyTorch, and includes the training logic.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q55321",
                                        "text": "'user': Formulate a Python coding plan to generate a geometric figure resembling a Rhombus shape., 'assistant': Sure, here's a brief outline of the Python code that could produce a Rhombus shape:\n\n1. Define a function, say `print_rhombus`, that takes in a parameter `n`. This `n` will determine the size of the rhombus. \n2. Inside this function, you'll have two for loops. The first loop will print the upper half of the rhombus (including the middle line), and the second loop will print the lower half of the rhombus.\n3. In both the loops, you'll print appropriate number of spaces followed by stars ('*') to create the rhombus shape.\n\nLet's put the plan in action and run the code.\n```python\ndef print_rhombus(n):\n    for i in range(n):\n        print(' ' * (n - i - 1) + '* ' * (i + 1))\n    for i in range(n - 1, -1, -1):\n        print(' ' * (n - i - 1) + '* ' * (i + 1))\n\nprint_rhombus(5)\n```, 'user': Execution result: \n    * \n   * * \n  * * * \n * * * * \n* * * * * \n* * * * * \n * * * * \n  * * * \n   * * \n    * \n, 'assistant': Here is the rhombus shape as per your request. The size of this rhombus is 5. You can alter the size by changing the parameter of the `print_rhombus()` function to fit your needs. Be aware that each '*' character paired with a space forms an individual unit of the rhombus. The amount of these units per line forms the rhombus' size as it spatially expands and contracts., 'user': The code works well and is formatted correctly, but it lacks comments. Adding comments would make it easier to understand."
                                },
                                "top_docs": [
                                        {
                                                "id": "c55321",
                                                "title": "",
                                                "text": "Certainly, it's critical to comment on the code for better understanding and maintenance. Here's the Python code of the rhombus shape generation with added comments:\n\n```python\ndef print_rhombus(n):\n    # Iterate from 0 to n for the top half of the rhombus\n    for i in range(n):\n        # Each line has (n - i) leading spaces followed by (i + 1) '*'\n        print(' ' * (n - i - 1) + '* ' * (i + 1))\n      \n    # Iterate from n-1 to 0 for the bottom half of the rhombus\n    for i in range(n - 1, -1, -1):\n        # Each line has (n - i) leading spaces followed by (i + 1) '*'\n        print(' ' * (n - i - 1) + '* ' * (i + 1))\n\n# Call the function with n = 5\nprint_rhombus(5)\n```\nThe comments explain the purpose of each line and the overall function to make the code understandable to other people reading it, or your future self!",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "codefeedback-st": {
                "name": "codefeedback-st",
                "description": "代码反馈（单轮）- 单轮对话中的代码改进建议",
                "samples": [
                        {
                                "query": {
                                        "id": "q39743",
                                        "text": "Implement an algorithm to sort the following array.\n[3, 5, 4, 6, 1, 2]"
                                },
                                "top_docs": [
                                        {
                                                "id": "d39743",
                                                "title": "",
                                                "text": "def sortArray(array):\n    n = len(array)\n    for i in range(n):\n        min_index = i\n        for j in range(i+1, n):\n            if array[min_index] > array[j]:\n                min_index = j\n        array[i], array[min_index] = array[min_index], array[i]\n    return array",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q3456",
                                        "text": "Construct a regex in Java that matches a valid email address and also validates the domain name. Additionally, the regex should check if the email address is from a popular email service provider such as Gmail, Yahoo, or Outlook, and return a specific error message if it is not. However, the regex should be implemented in a way that it does not rely on built-in regex libraries or functions available in Java."
                                },
                                "top_docs": [
                                        {
                                                "id": "d3456",
                                                "title": "",
                                                "text": "To construct a regex in Java without relying on built-in regex libraries or functions, you can use the following code:\n\n```java\npublic class EmailValidator {\n    public static void main(String[] args) {\n        String email = \"<EMAIL>\";\n        String errorMessage = \"Invalid email address or domain is not supported.\";\n\n        if (isValidEmail(email)) {\n            System.out.println(\"Valid email address.\");\n            if (isSupportedDomain(email)) {\n                System.out.println(\"Supported domain.\");\n            } else {\n                System.out.println(errorMessage);\n            }\n        } else {\n            System.out.println(errorMessage);\n        }\n    }\n\n    public static boolean isValidEmail(String email) {\n        String regex = \"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$\";\n        return email.matches(regex);\n    }\n\n    public static boolean isSupportedDomain(String email) {\n        String[] supportedDomains = {\"gmail.com\", \"yahoo.com\", \"outlook.com\"};\n        String domain = email.substring(email.lastIndexOf('@') + 1);\n        for (String supportedDomain : supportedDomains) {\n            if (domain.equalsIgnoreCase(supportedDomain)) {\n                return true;\n            }\n        }\n        return false;\n    }\n}\n```\n\nThis code validates an email address using a regular expression `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$`. It also checks if the domain is supported by comparing it with a list of popular email service provider domains: Gmail, Yahoo, and Outlook. If the email is valid but the domain is not supported, it returns the specified error message.\n\nNote that this approach is not as robust as using a dedicated regex library. If you have the option to use built-in regex libraries or functions, it is recommended to use them instead for better accuracy and maintainability.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q149792",
                                        "text": "Create a function that generates a dynamic SQL statement based on user input to retrieve records from multiple tables and perform a complex join operation. The function should also include the ability to filter results using parameters passed in by the user."
                                },
                                "top_docs": [
                                        {
                                                "id": "d149792",
                                                "title": "",
                                                "text": "Here's an example function that generates a dynamic SQL statement based on user input to retrieve records from multiple tables and perform a complex join operation. The function also includes the ability to filter results using parameters passed in by the user. \n```python\ndef generate_sql_statement(table_list, join_conditions, filter_conditions):\n    # table_list: list of tables to join\n    # join_conditions: list of join conditions\n    # filter_conditions: dictionary of filter conditions (key = column name, value = filter value)\n    # Generate SELECT statement\n    select_statement = \"SELECT * FROM \"\n    # Add table names to SELECT statement\n    for i, table_name in enumerate(table_list):\n        if i == 0:\n            select_statement += table_name\n        else:\n            select_statement += \" JOIN \" + table_name\n    # Generate JOIN statement\n    join_statement = \" ON \".join(join_conditions)\n    # Generate WHERE statement\n    where_conditions = []\n    for column_name, filter_value in filter_conditions.items():\n        where_conditions.append(column_name + \" = \" + filter_value)\n    where_statement = \" AND \".join(where_conditions)\n    # Combine all statements into final SQL statement\n    sql_statement = select_statement + \" ON \" + join_statement\n    if filter_conditions:\n        sql_statement += \" WHERE \" + where_statement\n    return sql_statement\n```\nExample usage:\n```python\ntable_list = [\"orders\", \"customers\", \"products\"]\njoin_conditions = [\"orders.customer_id = customers.customer_id\", \"orders.product_id = products.product_id\"]\nfilter_conditions = {\"customers.country\": \"'USA'\", \"products.category\": \"'Electronics'\"}\nsql_statement = generate_sql_statement(table_list, join_conditions, filter_conditions)\nprint(sql_statement)\n```\nOutput:\n```\nSELECT * FROM orders JOIN customers ON orders.customer_id = customers.customer_id JOIN products ON orders.product_id = products.product_id WHERE customers.country = 'USA' AND products.category = 'Electronics'\n```",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "codetrans-contest": {
                "name": "codetrans-contest",
                "description": "代码竞赛翻译 - 竞赛级别的代码翻译任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q183",
                                        "text": "def quad(top=2200):\n    r = [False] * top\n    ab = [False] * (top * 2)**2\n    for a in range(1, top):\n        for b in range(a, top):\n            ab[a * a + b * b] = True\n    s = 3\n    for c in range(1, top):\n        s1, s, s2 = s, s + 2, s + 2\n        for d in range(c + 1, top):\n            if ab[s1]:\n                r[d] = True\n            s1 += s2\n            s2 += 2\n    return [i for i, val in enumerate(r) if not val and i]\n    \nif __name__ == '__main__':\n    n = 2200\n    print(f\"Those values of d in 1..{n} that can't be represented: {quad(n)}\")\n"
                                },
                                "top_docs": [
                                        {
                                                "id": "c218",
                                                "title": "Pythagorean quadruples",
                                                "text": "#include <iostream>\n#include <vector>\n\nconstexpr int N = 2200;\nconstexpr int N2 = 2 * N * N;\n\nint main() {\n    using namespace std;\n\n    vector<bool> found(N + 1);\n    vector<bool> aabb(N2 + 1);\n\n    int s = 3;\n\n    for (int a = 1; a < N; ++a) {\n        int aa = a * a;\n        for (int b = 1; b < N; ++b) {\n            aabb[aa + b * b] = true;\n        }\n    }\n\n    for (int c = 1; c <= N; ++c) {\n        int s1 = s;\n        s += 2;\n        int s2 = s;\n        for (int d = c + 1; d <= N; ++d) {\n            if (aabb[s1]) {\n                found[d] = true;\n            }\n            s1 += s2;\n            s2 += 2;\n        }\n    }\n\n    cout << \"The values of d <= \" << N << \" which can't be represented:\" << endl;\n    for (int d = 1; d <= N; ++d) {\n        if (!found[d]) {\n            cout << d << \" \";\n        }\n    }\n    cout << endl;\n\n    return 0;\n}\n",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q98",
                                        "text": "\n\n\n\n\ndef _init():\n    \"digit sections for forming numbers\"\n    digi_bits = .strip()\n\n    lines = [[d.replace('.', ' ') for d in ln.strip().split()]\n             for ln in digi_bits.strip().split('\\n')\n             if '\n    formats = '<2 >2 <2 >2'.split()\n    digits = [[f\"{dig:{f}}\" for dig in line]\n              for f, line in zip(formats, lines)]\n\n    return digits\n\n_digits = _init()\n\n\n\ndef _to_digits(n):\n    assert 0 <= n < 10_000 and int(n) == n\n    \n    return [int(digit) for digit in f\"{int(n):04}\"][::-1]\n\ndef num_to_lines(n):\n    global _digits\n    d = _to_digits(n)\n    lines = [\n        ''.join((_digits[1][d[1]], '┃',  _digits[0][d[0]])),\n        ''.join((_digits[0][   0], '┃',  _digits[0][   0])),\n        ''.join((_digits[3][d[3]], '┃',  _digits[2][d[2]])),\n        ]\n    \n    return lines\n\ndef cjoin(c1, c2, spaces='   '):\n    return [spaces.join(by_row) for by_row in zip(c1, c2)]\n\n\nif __name__ == '__main__':\n    \n    \n    \n    \n    for pow10 in range(4):    \n        step = 10 ** pow10\n        print(f'\\nArabic {step}-to-{9*step} by {step} in Cistercian:\\n')\n        lines = num_to_lines(step)\n        for n in range(step*2, step*10, step):\n            lines = cjoin(lines, num_to_lines(n))\n        print('\\n'.join(lines))\n    \n\n    numbers = [0, 5555, 6789, 6666]\n    print(f'\\nArabic {str(numbers)[1:-1]} in Cistercian:\\n')\n    lines = num_to_lines(numbers[0])\n    for n in numbers[1:]:\n        lines = cjoin(lines, num_to_lines(n))\n    print('\\n'.join(lines))\n"
                                },
                                "top_docs": [
                                        {
                                                "id": "c130",
                                                "title": "Cistercian numerals",
                                                "text": "#include <array>\n#include <iostream>\n\ntemplate<typename T, size_t S>\nusing FixedSquareGrid = std::array<std::array<T, S>, S>;\n\nstruct Cistercian {\npublic:\n    Cistercian() {\n        initN();\n    }\n\n    Cistercian(int v) {\n        initN();\n        draw(v);\n    }\n\n    Cistercian &operator=(int v) {\n        initN();\n        draw(v);\n    }\n\n    friend std::ostream &operator<<(std::ostream &, const Cistercian &);\n\nprivate:\n    FixedSquareGrid<char, 15> canvas;\n\n    void initN() {\n        for (auto &row : canvas) {\n            row.fill(' ');\n            row[5] = 'x';\n        }\n    }\n\n    void horizontal(size_t c1, size_t c2, size_t r) {\n        for (size_t c = c1; c <= c2; c++) {\n            canvas[r][c] = 'x';\n        }\n    }\n\n    void vertical(size_t r1, size_t r2, size_t c) {\n        for (size_t r = r1; r <= r2; r++) {\n            canvas[r][c] = 'x';\n        }\n    }\n\n    void diagd(size_t c1, size_t c2, size_t r) {\n        for (size_t c = c1; c <= c2; c++) {\n            canvas[r + c - c1][c] = 'x';\n        }\n    }\n\n    void diagu(size_t c1, size_t c2, size_t r) {\n        for (size_t c = c1; c <= c2; c++) {\n            canvas[r - c + c1][c] = 'x';\n        }\n    }\n\n    void drawOnes(int v) {\n        switch (v) {\n        case 1:\n            horizontal(6, 10, 0);\n            break;\n        case 2:\n            horizontal(6, 10, 4);\n            break;\n        case 3:\n            diagd(6, 10, 0);\n            break;\n        case 4:\n            diagu(6, 10, 4);\n            break;\n        case 5:\n            drawOnes(1);\n            drawOnes(4);\n            break;\n        case 6:\n            vertical(0, 4, 10);\n            break;\n        case 7:\n            drawOnes(1);\n            drawOnes(6);\n            break;\n        case 8:\n            drawOnes(2);\n            drawOnes(6);\n            break;\n        case 9:\n            drawOnes(1);\n            drawOnes(8);\n            break;\n        default:\n            break;\n        }\n    }\n\n    void drawTens(int v) {\n        switch (v) {\n        case 1:\n            horizontal(0, 4, 0);\n            break;\n        case 2:\n            horizontal(0, 4, 4);\n            break;\n        case 3:\n            diagu(0, 4, 4);\n            break;\n        case 4:\n            diagd(0, 4, 0);\n            break;\n        case 5:\n            drawTens(1);\n            drawTens(4);\n            break;\n        case 6:\n            vertical(0, 4, 0);\n            break;\n        case 7:\n            drawTens(1);\n            drawTens(6);\n            break;\n        case 8:\n            drawTens(2);\n            drawTens(6);\n            break;\n        case 9:\n            drawTens(1);\n            drawTens(8);\n            break;\n        default:\n            break;\n        }\n    }\n\n    void drawHundreds(int hundreds) {\n        switch (hundreds) {\n        case 1:\n            horizontal(6, 10, 14);\n            break;\n        case 2:\n            horizontal(6, 10, 10);\n            break;\n        case 3:\n            diagu(6, 10, 14);\n            break;\n        case 4:\n            diagd(6, 10, 10);\n            break;\n        case 5:\n            drawHundreds(1);\n            drawHundreds(4);\n            break;\n        case 6:\n            vertical(10, 14, 10);\n            break;\n        case 7:\n            drawHundreds(1);\n            drawHundreds(6);\n            break;\n        case 8:\n            drawHundreds(2);\n            drawHundreds(6);\n            break;\n        case 9:\n            drawHundreds(1);\n            drawHundreds(8);\n            break;\n        default:\n            break;\n        }\n    }\n\n    void drawThousands(int thousands) {\n        switch (thousands) {\n        case 1:\n            horizontal(0, 4, 14);\n            break;\n        case 2:\n            horizontal(0, 4, 10);\n            break;\n        case 3:\n            diagd(0, 4, 10);\n            break;\n        case 4:\n            diagu(0, 4, 14);\n            break;\n        case 5:\n            drawThousands(1);\n            drawThousands(4);\n            break;\n        case 6:\n            vertical(10, 14, 0);\n            break;\n        case 7:\n            drawThousands(1);\n            drawThousands(6);\n            break;\n        case 8:\n            drawThousands(2);\n            drawThousands(6);\n            break;\n        case 9:\n            drawThousands(1);\n            drawThousands(8);\n            break;\n        default:\n            break;\n        }\n    }\n\n    void draw(int v) {\n        int thousands = v / 1000;\n        v %= 1000;\n\n        int hundreds = v / 100;\n        v %= 100;\n\n        int tens = v / 10;\n        int ones = v % 10;\n\n        if (thousands > 0) {\n            drawThousands(thousands);\n        }\n        if (hundreds > 0) {\n            drawHundreds(hundreds);\n        }\n        if (tens > 0) {\n            drawTens(tens);\n        }\n        if (ones > 0) {\n            drawOnes(ones);\n        }\n    }\n};\n\nstd::ostream &operator<<(std::ostream &os, const Cistercian &c) {\n    for (auto &row : c.canvas) {\n        for (auto cell : row) {\n            os << cell;\n        }\n        os << '\\n';\n    }\n    return os;\n}\n\nint main() {\n    for (auto number : { 0, 1, 20, 300, 4000, 5555, 6789, 9999 }) {\n        std::cout << number << \":\\n\";\n\n        Cistercian c(number);\n        std::cout << c << '\\n';\n    }\n\n    return 0;\n}\n",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q302",
                                        "text": "import numpy as np\nimport matplotlib.pyplot as plt\nfrom matplotlib.colors import hsv_to_rgb as hsv\n\ndef curve(axiom, rules, angle, depth):\n    for _ in range(depth):\n        axiom = ''.join(rules[c] if c in rules else c for c in axiom)\n\n    a, x, y = 0, [0], [0]\n    for c in axiom:\n        match c:\n            case '+':\n                a += 1\n            case '-':\n                a -= 1\n            case 'F' | 'G':\n                x.append(x[-1] + np.cos(a*angle*np.pi/180))\n                y.append(y[-1] + np.sin(a*angle*np.pi/180))\n\n    l = len(x)\n    \n    for i in range(l - 1):\n        plt.plot(x[i:i+2], y[i:i+2], color=hsv([i/l, 1, .7]))\n    plt.gca().set_aspect(1)\n    plt.show()\n\ncurve('F--XF--F--XF', {'X': 'XF+G+XF--F--XF+G+X'}, 45, 5)\n\n\n\n\n"
                                },
                                "top_docs": [
                                        {
                                                "id": "c269",
                                                "title": "Sierpinski curve",
                                                "text": "\n#include <cmath>\n#include <fstream>\n#include <iostream>\n#include <string>\n\nclass sierpinski_curve {\npublic:\n    void write(std::ostream& out, int size, int length, int order);\nprivate:\n    static std::string rewrite(const std::string& s);\n    void line(std::ostream& out);\n    void execute(std::ostream& out, const std::string& s);\n    double x_;\n    double y_;\n    int angle_;\n    int length_;\n};\n\nvoid sierpinski_curve::write(std::ostream& out, int size, int length, int order) {\n    length_ = length;\n    x_ = length/std::sqrt(2.0);\n    y_ = 2 * x_;\n    angle_ = 45;\n    out << \"<svg xmlns='http:\n        << size << \"' height='\" << size << \"'>\\n\";\n    out << \"<rect width='100%' height='100%' fill='white'/>\\n\";\n    out << \"<path stroke-width='1' stroke='black' fill='none' d='\";\n    std::string s = \"F--XF--F--XF\";\n    for (int i = 0; i < order; ++i)\n        s = rewrite(s);\n    execute(out, s);\n    out << \"'/>\\n</svg>\\n\";\n}\n\nstd::string sierpinski_curve::rewrite(const std::string& s) {\n    std::string t;\n    for (char c : s) {\n        if (c == 'X')\n            t += \"XF+G+XF--F--XF+G+X\";\n        else\n            t += c;\n    }\n    return t;\n}\n\nvoid sierpinski_curve::line(std::ostream& out) {\n    double theta = (3.14159265359 * angle_)/180.0;\n    x_ += length_ * std::cos(theta);\n    y_ -= length_ * std::sin(theta);\n    out << \" L\" << x_ << ',' << y_;\n}\n\nvoid sierpinski_curve::execute(std::ostream& out, const std::string& s) {\n    out << 'M' << x_ << ',' << y_;\n    for (char c : s) {\n        switch (c) {\n        case 'F':\n        case 'G':\n            line(out);\n            break;\n        case '+':\n            angle_ = (angle_ + 45) % 360;\n            break;\n        case '-':\n            angle_ = (angle_ - 45) % 360;\n            break;\n        }\n    }\n}\n\nint main() {\n    std::ofstream out(\"sierpinski_curve.svg\");\n    if (!out) {\n        std::cerr << \"Cannot open output file\\n\";\n        return 1;\n    }\n    sierpinski_curve s;\n    s.write(out, 545, 7, 5);\n    return 0;\n}\n",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "synthetic-text2sql": {
                "name": "synthetic-text2sql",
                "description": "文本到SQL - 将自然语言查询转换为SQL语句",
                "samples": [
                        {
                                "query": {
                                        "id": "q102231",
                                        "text": "What was the distribution of attendees by age group for each event in '2021'?"
                                },
                                "top_docs": [
                                        {
                                                "id": "c102231",
                                                "title": "",
                                                "text": "SELECT event_id, age_group, COUNT(*) AS num_attendees FROM Attendees WHERE YEAR(attendee_date) = 2021 GROUP BY event_id, age_group;",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q103599",
                                        "text": "Who are the top 5 goal scorers in the 2022 FIFA World Cup?"
                                },
                                "top_docs": [
                                        {
                                                "id": "c103599",
                                                "title": "",
                                                "text": "SELECT player, goals FROM world_cup_goals WHERE world_cup = true ORDER BY goals DESC LIMIT 5;",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q105521",
                                        "text": "What is the average age of patients who improved after therapy?"
                                },
                                "top_docs": [
                                        {
                                                "id": "c105521",
                                                "title": "",
                                                "text": "SELECT AVG(age) FROM patients WHERE improvement = 'Y';",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "cosqa": {
                "name": "cosqa",
                "description": "代码搜索问答 - 基于自然语言查询搜索相关代码",
                "samples": [
                        {
                                "query": {
                                        "id": "q20435",
                                        "text": "python default menuitem select"
                                },
                                "top_docs": [
                                        {
                                                "id": "d20435",
                                                "title": "",
                                                "text": "def get_python(self):\n        \"\"\"Only return cursor instance if configured for multiselect\"\"\"\n        if self.multiselect:\n            return super(MultiSelectField, self).get_python()\n\n        return self._get()",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q20398",
                                        "text": "iterator is past the end python"
                                },
                                "top_docs": [
                                        {
                                                "id": "d20398",
                                                "title": "",
                                                "text": "def __next__(self):\n    \"\"\"Pop the head off the iterator and return it.\"\"\"\n    res = self._head\n    self._fill()\n    if res is None:\n      raise StopIteration()\n    return res",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q20313",
                                        "text": "how to compute the minimum value of a tensor in python"
                                },
                                "top_docs": [
                                        {
                                                "id": "d20313",
                                                "title": "",
                                                "text": "def last_location_of_minimum(x):\n    \"\"\"\n    Returns the last location of the minimal value of x.\n    The position is calculated relatively to the length of x.\n\n    :param x: the time series to calculate the feature of\n    :type x: numpy.ndarray\n    :return: the value of this feature\n    :return type: float\n    \"\"\"\n    x = np.asarray(x)\n    return 1.0 - np.argmin(x[::-1]) / len(x) if len(x) > 0 else np.NaN",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-go": {
                "name": "CodeSearchNet-go",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q181865",
                                        "text": "func DefaultParser(tx *Xslate, args Args) error {\n\tsyntax, ok := args.Get(\"Syntax\")\n\tif !ok {\n\t\tsyntax = \"TTerse\"\n\t}\n\n\tswitch syntax {\n\tcase \"TTerse\":\n\t\ttx.Parser = tterse.New()\n\tcase \"Kolon\", \"Kolonish\":\n\t\ttx.Parser = kolonish.New()\n\tdefault:\n\t\treturn errors.New(\"sytanx '\" + syntax.(string) + \"' is not available\")\n\t}\n\treturn nil\n}"
                                },
                                "top_docs": [
                                        {
                                                "id": "c181585",
                                                "title": "",
                                                "text": "// DefaultParser sets up and assigns the default parser to be used by Xslate.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q179747",
                                        "text": "func (p *SetBlockedURLSParams) Do(ctx context.Context) (err error) {\n\treturn cdp.Execute(ctx, CommandSetBlockedURLS, p, nil)\n}"
                                },
                                "top_docs": [
                                        {
                                                "id": "c179487",
                                                "title": "",
                                                "text": "// Do executes Network.setBlockedURLs against the provided context.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q181502",
                                        "text": "func (ub *OutgoingUnbanChatMember) Send() error {\n\tresp := &baseResponse{}\n\t_, err := ub.api.c.postJSON(unbanChatMember, resp, ub)\n\n\tif err != nil {\n\t\treturn err\n\t}\n\n\treturn check(resp)\n}"
                                },
                                "top_docs": [
                                        {
                                                "id": "c181223",
                                                "title": "",
                                                "text": "// Send sends the unban request.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-java": {
                "name": "CodeSearchNet-java",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q175161",
                                        "text": "public void addHeading(String heading, int row) {\n    layoutComponents.add( new LayoutComponent( heading, 0, row, null));\n  }"
                                },
                                "top_docs": [
                                        {
                                                "id": "c175010",
                                                "title": "",
                                                "text": "Add a heading at the specified row. this spans all columns",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q176183",
                                        "text": "public RTreeIndexTableDao getTableDao(FeatureDao featureDao) {\n\n        GeoPackageConnection connection = getGeoPackage().getConnection();\n        UserCustomConnection userDb = new UserCustomConnection(connection);\n        UserCustomTable userCustomTable = getRTreeTable(featureDao.getTable());\n        UserCustomDao userCustomDao = new UserCustomDao(geoPackage.getName(),\n                connection, userDb, userCustomTable);\n\n        return new RTreeIndexTableDao(this, userCustomDao, featureDao);\n    }"
                                },
                                "top_docs": [
                                        {
                                                "id": "c176031",
                                                "title": "",
                                                "text": "Get a RTree Index Table DAO for the feature dao\n\n@param featureDao feature DAO\n@return RTree Index Table DAO\n@since 3.1.0",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q180111",
                                        "text": "public static Optional<Charset> charset(File file) {\n        if (!file.exists()) {\n            logger.error(\"The file [ {} ] is not exist.\", file.getAbsolutePath());\n            return Optional.absent();\n        }\n\n        FileInputStream fileInputStream = null;\n        BufferedInputStream bin = null;\n        try {\n            fileInputStream = new FileInputStream(file);\n            bin = new BufferedInputStream(fileInputStream);\n            int p = (bin.read() << 8) + bin.read();\n            Optional<Charset> charset;\n            //其中的 0xefbb、0xfffe、0xfeff、0x5c75这些都是这个文件的前面两个字节的16进制数\n            switch (p) {\n                case 0xefbb:\n                    charset = Optional.of(Charsets.UTF_8);\n                    break;\n                case 0xfffe:\n                    charset = Optional.of(Charset.forName(\"Unicode\"));\n                    break;\n                case 0xfeff:\n                    charset = Optional.of(Charsets.UTF_16BE);\n                    break;\n                case 0x5c75:\n                    charset = Optional.of(Charsets.US_ASCII);\n                    break;\n                default:\n                    charset = Optional.of(Charset.forName(\"GBK\"));\n            }\n\n            return charset;\n        } catch (FileNotFoundException e) {\n            logger.error(\"The file [ {} ] is not exist.\", file.getAbsolutePath(), e);\n        } catch (IOException e) {\n            logger.error(\"Read file has error, {}.\", file.getAbsolutePath(), e);\n        } finally {\n            IOUtils.closeQuietly(fileInputStream);\n            IOUtils.closeQuietly(bin);\n        }\n\n        return Optional.absent();\n    }"
                                },
                                "top_docs": [
                                        {
                                                "id": "c179926",
                                                "title": "",
                                                "text": "Get coding format file.\n\n@param file the file.\n@return File encoding format.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-javascript": {
                "name": "CodeSearchNet-javascript",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q63139",
                                        "text": "function addUnsavedRecords(record, key, data) {\n  if(record) {\n    data.pushObjects(record.get(key).filterBy('isNew'));\n  }\n}"
                                },
                                "top_docs": [
                                        {
                                                "id": "c62858",
                                                "title": "",
                                                "text": "If there are any unsaved records that are in a hasMany they won't be in the payload, so add them back in manually.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q64171",
                                        "text": "function Template(str, options) {\n\n  // Handle the case where the only argument passed is the `options` object\n  if(_.isObject(str) && !options){\n    options = str;\n    str = null;\n  }\n\n  // Create options if not provided\n  options = options ? _.clone(options) : {};\n\n  // Set default cache behavior\n  // if node\n  if(!_.isBoolean(options.cache)) {\n    options.cache = process.env.NODE_ENV === 'production';\n  }\n  // end\n\n  // Merges given `options` with `DEFAULTS`\n  options = _.defaults(options, DEFAULTS);\n  options.cacheContext = options.cacheContext || Template;\n\n  // Sets instance variables\n  this.template = str;\n  this.options = options;\n  this._compiled = null;\n\n  // Creates the cache if not already done\n  if(options.cache && !(this._getCache() instanceof options.cacheHandler)) {\n    var cacheOptions = [options.cacheHandler].concat(options.cacheOptions);\n    options.cacheContext[options._cacheProp] = typeof window !== 'undefined' ?\n                                                 new options.cacheHandler() :\n                                                 construct.apply(this,\n                                                                 cacheOptions);\n  }\n}"
                                },
                                "top_docs": [
                                        {
                                                "id": "c63865",
                                                "title": "",
                                                "text": "Initializes `Template` with optionnally the given `str` and\n`options`.\n\n@param {String} [str]\n@param {Object} [options]\n@api public",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q64624",
                                        "text": "function getIgnored(filepath) {\n      for (var i in options.ignore) {\n        if (filepath.indexOf(options.ignore[i]) !== -1) {\n          return options.ignore[i];\n        }\n      }\n      return null;\n    }"
                                },
                                "top_docs": [
                                        {
                                                "id": "c64309",
                                                "title": "",
                                                "text": "Validates \"ignore\" option for given path\n@param filepath\n@returns {*}",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-ruby": {
                "name": "CodeSearchNet-ruby",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q26657",
                                        "text": "def measure_internal(level, index, message, params)\n      exception = nil\n      result    = nil\n      # Single parameter is a hash\n      if params.empty? && message.is_a?(Hash)\n        params  = message\n        message = nil\n      end\n      start = Process.clock_gettime(Process::CLOCK_MONOTONIC)\n      begin\n        if block_given?\n          result =\n            if (silence_level = params[:silence])\n              # In case someone accidentally sets `silence: true` instead of `silence: :error`\n              silence_level = :error if silence_level == true\n              silence(silence_level) { yield(params) }\n            else\n              yield(params)\n            end\n        end\n      rescue Exception => exc\n        exception = exc\n      ensure\n        # Must use ensure block otherwise a `return` in the yield above will skip the log entry\n        log       = Log.new(name, level, index)\n        exception ||= params[:exception]\n        message   = params[:message] if params[:message]\n        duration  =\n          if block_given?\n            1_000.0 * (Process.clock_gettime(Process::CLOCK_MONOTONIC) - start)\n          else\n            params[:duration] || raise('Mandatory block missing when :duration option is not supplied')\n          end\n\n        # Extract options after block completes so that block can modify any of the options\n        payload = params[:payload]\n\n        # May return false due to elastic logging\n        should_log = log.assign(\n          message:            message,\n          payload:            payload,\n          min_duration:       params[:min_duration] || 0.0,\n          exception:          exception,\n          metric:             params[:metric],\n          metric_amount:      params[:metric_amount],\n          duration:           duration,\n          log_exception:      params[:log_exception] || :partial,\n          on_exception_level: params[:on_exception_level]\n        )\n\n        # Log level may change during assign due to :on_exception_level\n        self.log(log) if should_log && should_log?(log)\n        raise exception if exception\n        result\n      end\n    end"
                                },
                                "top_docs": [
                                        {
                                                "id": "c26647",
                                                "title": "",
                                                "text": "Measure the supplied block and log the message",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q26683",
                                        "text": "def import_all(source)\n      names = source.public_methods - Registry.instance_methods - Module.methods\n      names -= [:initialize] # for compatibility with Rubinius\n      names += source.store.methods.keys if source.is_a? Registry\n\n      import_methods(source, names)\n    end"
                                },
                                "top_docs": [
                                        {
                                                "id": "c26673",
                                                "title": "",
                                                "text": "Creates new immutable collection from the current one,\n updated with all singleton methods and imported methods\n from the other module\n\n @param [Module] source The module to import procedures from\n\n @return [Transproc::Store]",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q26781",
                                        "text": "def padout(message)\n      message_length = self.class.display_columns(message)\n\n      if @last_render_width > message_length\n        remaining_width = @last_render_width - message_length\n        message += ' ' * remaining_width\n      end\n      message\n    end"
                                },
                                "top_docs": [
                                        {
                                                "id": "c26769",
                                                "title": "",
                                                "text": "Pad message out with spaces\n\n @api private",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-python": {
                "name": "CodeSearchNet-python",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q267457",
                                        "text": "def update_key(\n            self, vault_base_url, key_name, key_version, key_ops=None, key_attributes=None, tags=None, custom_headers=None, raw=False, **operation_config):\n        \"\"\"The update key operation changes specified attributes of a stored key\n        and can be applied to any key type and key version stored in Azure Key\n        Vault.\n\n        In order to perform this operation, the key must already exist in the\n        Key Vault. Note: The cryptographic material of a key itself cannot be\n        changed. This operation requires the keys/update permission.\n\n        :param vault_base_url: The vault name, for example\n         https://myvault.vault.azure.net.\n        :type vault_base_url: str\n        :param key_name: The name of key to update.\n        :type key_name: str\n        :param key_version: The version of the key to update.\n        :type key_version: str\n        :param key_ops: Json web key operations. For more information on\n         possible key operations, see JsonWebKeyOperation.\n        :type key_ops: list[str or\n         ~azure.keyvault.v2016_10_01.models.JsonWebKeyOperation]\n        :param key_attributes:\n        :type key_attributes: ~azure.keyvault.v2016_10_01.models.KeyAttributes\n        :param tags: Application specific metadata in the form of key-value\n         pairs.\n        :type tags: dict[str, str]\n        :param dict custom_headers: headers that will be added to the request\n        :param bool raw: returns the direct response alongside the\n         deserialized response\n        :param operation_config: :ref:`Operation configuration\n         overrides<msrest:optionsforoperations>`.\n        :return: KeyBundle or ClientRawResponse if raw=true\n        :rtype: ~azure.keyvault.v2016_10_01.models.KeyBundle or\n         ~msrest.pipeline.ClientRawResponse\n        :raises:\n         :class:`KeyVaultErrorException<azure.keyvault.v2016_10_01.models.KeyVaultErrorException>`\n        \"\"\"\n        parameters = models.KeyUpdateParameters(key_ops=key_ops, key_attributes=key_attributes, tags=tags)\n\n        # Construct URL\n        url = self.update_key.metadata['url']\n        path_format_arguments = {\n            'vaultBaseUrl': self._serialize.url(\"vault_base_url\", vault_base_url, 'str', skip_quote=True),\n            'key-name': self._serialize.url(\"key_name\", key_name, 'str'),\n            'key-version': self._serialize.url(\"key_version\", key_version, 'str')\n        }\n        url = self._client.format_url(url, **path_format_arguments)\n\n        # Construct parameters\n        query_parameters = {}\n        query_parameters['api-version'] = self._serialize.query(\"self.api_version\", self.api_version, 'str')\n\n        # Construct headers\n        header_parameters = {}\n        header_parameters['Content-Type'] = 'application/json; charset=utf-8'\n        if self.config.generate_client_request_id:\n            header_parameters['x-ms-client-request-id'] = str(uuid.uuid1())\n        if custom_headers:\n            header_parameters.update(custom_headers)\n        if self.config.accept_language is not None:\n            header_parameters['accept-language'] = self._serialize.header(\"self.config.accept_language\", self.config.accept_language, 'str')\n\n        # Construct body\n        body_content = self._serialize.body(parameters, 'KeyUpdateParameters')\n\n        # Construct and send request\n        request = self._client.patch(url, query_parameters)\n        response = self._client.send(\n            request, header_parameters, body_content, stream=False, **operation_config)\n\n        if response.status_code not in [200]:\n            raise models.KeyVaultErrorException(self._deserialize, response)\n\n        deserialized = None\n\n        if response.status_code == 200:\n            deserialized = self._deserialize('KeyBundle', response)\n\n        if raw:\n            client_raw_response = ClientRawResponse(deserialized, response)\n            return client_raw_response\n\n        return deserialized"
                                },
                                "top_docs": [
                                        {
                                                "id": "c267318",
                                                "title": "",
                                                "text": "The update key operation changes specified attributes of a stored key\n        and can be applied to any key type and key version stored in Azure Key\n        Vault.\n\n        In order to perform this operation, the key must already exist in the\n        Key Vault. Note: The cryptographic material of a key itself cannot be\n        changed. This operation requires the keys/update permission.\n\n        :param vault_base_url: The vault name, for example\n         https://myvault.vault.azure.net.\n        :type vault_base_url: str\n        :param key_name: The name of key to update.\n        :type key_name: str\n        :param key_version: The version of the key to update.\n        :type key_version: str\n        :param key_ops: Json web key operations. For more information on\n         possible key operations, see JsonWebKeyOperation.\n        :type key_ops: list[str or\n         ~azure.keyvault.v2016_10_01.models.JsonWebKeyOperation]\n        :param key_attributes:\n        :type key_attributes: ~azure.keyvault.v2016_10_01.models.KeyAttributes\n        :param tags: Application specific metadata in the form of key-value\n         pairs.\n        :type tags: dict[str, str]\n        :param dict custom_headers: headers that will be added to the request\n        :param bool raw: returns the direct response alongside the\n         deserialized response\n        :param operation_config: :ref:`Operation configuration\n         overrides<msrest:optionsforoperations>`.\n        :return: KeyBundle or ClientRawResponse if raw=true\n        :rtype: ~azure.keyvault.v2016_10_01.models.KeyBundle or\n         ~msrest.pipeline.ClientRawResponse\n        :raises:\n         :class:`KeyVaultErrorException<azure.keyvault.v2016_10_01.models.KeyVaultErrorException>`",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q269706",
                                        "text": "def save_authorization_code(self, client_id, code, request,\n                                *args, **kwargs):\n        \"\"\"Persist the authorization code.\"\"\"\n        log.debug(\n            'Persist authorization code %r for client %r',\n            code, client_id\n        )\n        request.client = request.client or self._clientgetter(client_id)\n        self._grantsetter(client_id, code, request, *args, **kwargs)\n        return request.client.default_redirect_uri"
                                },
                                "top_docs": [
                                        {
                                                "id": "c269558",
                                                "title": "",
                                                "text": "Persist the authorization code.",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q274443",
                                        "text": "def _is_big_enough(image, size):\n    \"\"\"Check that the image's size superior to `size`\"\"\"\n    if (size[0] > image.size[0]) and (size[1] > image.size[1]):\n        raise ImageSizeError(image.size, size)"
                                },
                                "top_docs": [
                                        {
                                                "id": "c274260",
                                                "title": "",
                                                "text": "Check that the image's size superior to `size`",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-php": {
                "name": "CodeSearchNet-php",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q255799",
                                        "text": "public function getClassProperties($props, $indent = \"\\t\")\n    {\n        $code = $indent.\"\\n\";\n\n        foreach ($props as $prop) {\n            if (!empty($prop['docs'])) {\n                $code .= $indent.$this->getDocBlock($prop['docs'], $indent);\n            }\n            $code .= $indent.'public $'.$prop['name'].\";\\n\";\n        }\n        return $code;\n    }"
                                },
                                "top_docs": [
                                        {
                                                "id": "c255547",
                                                "title": "",
                                                "text": "Return class properties from array with indent specified\n\n@param array $props  Properties array\n@param array $indent Indentation in tabs\n\n@return string",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q258796",
                                        "text": "public function setCreationDate($time)\n    {\n        if (empty($time) && !is_numeric($time)) {\n            $this->creationDate = null;\n\n            return $this;\n        }\n\n        if (is_string($time)) {\n            try {\n                $time = new DateTime($time);\n            } catch (Exception $e) {\n                throw new InvalidArgumentException(sprintf(\n                    'Invalid Creation Date: %s',\n                    $e->getMessage()\n                ), $e->getCode(), $e);\n            }\n        }\n\n        if (!$time instanceof DateTimeInterface) {\n            throw new InvalidArgumentException(\n                'Creation Date must be a date/time string or an instance of DateTimeInterface'\n            );\n        }\n\n        $this->creationDate = $time;\n\n        return $this;\n    }"
                                },
                                "top_docs": [
                                        {
                                                "id": "c258512",
                                                "title": "",
                                                "text": "Set the route's last creation date.\n\n@param  string|DateTimeInterface|null $time The date/time value.\n@throws InvalidArgumentException If the date/time value is invalid.\n@return self",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q254461",
                                        "text": "public function getMonths(bool $raw = false)\n\t{\n\t\tif ($raw)\n\t\t{\n\t\t\treturn $this->difference / MONTH;\n\t\t}\n\n\t\t$time = clone($this->currentTime);\n\t\treturn $time->fieldDifference($this->testTime, IntlCalendar::FIELD_MONTH);\n\t}"
                                },
                                "top_docs": [
                                        {
                                                "id": "c254215",
                                                "title": "",
                                                "text": "Returns the number of months difference between the two dates.\n\n@param boolean $raw\n\n@return float|integer",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-ccr-go": {
                "name": "CodeSearchNet-ccr-go",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q177272",
                                        "text": "func rollback(tx *sql.Tx, reason error) error {\n\terr := tx.Rollback()\n\tif err != nil {\n\t\tlogger.Warnf(\"Failed to rollback transaction"
                                },
                                "top_docs": [
                                        {
                                                "id": "c177272",
                                                "title": "rollback",
                                                "text": " after error (%v): %v\", reason, err)\n\t}\n\n\treturn reason\n}",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q177956",
                                        "text": "func (c *openshiftClient) doRequest(ctx context.Context, method, path string, requestBody []byte) ([]byte, error) {\n\turl := *c.baseURL\n\turl.Path = path\n\tvar requestBodyReader io.Reader\n\tif requestBody != nil {\n\t\tlogrus.Debugf(\"Will send body: %s\", requestBody)\n\t\trequestBodyReader = bytes.NewReader(requestBody)\n\t}\n\treq, err := http.NewRequest(method, url.String(), requestBodyReader)\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\treq = req.WithContext(ctx)\n\n\tif len(c.bearerToken) != 0 {\n\t\treq.Header.Set(\"Authorization\", \"Bearer \"+c.bearerToken)\n\t} else if len(c.username) != 0 {\n\t\treq.SetBasicAuth(c.username, c.password)\n\t}\n\treq.Header.Set(\"Accept\", \"application/json, */*\")\n\treq.Header.Set(\"User-Agent\", fmt.Sprintf(\"skopeo/%s\", version.Version))\n\tif requestBody != nil {\n\t\treq.Header.Set(\"Content-Type\", \"application/json\")\n\t}\n\n\tlogrus.Debugf(\"%s %s\", method, url.String())\n\tres, err := c.httpClient.Do(req)\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\tdefer res.Body.Close()\n\tbody, err := ioutil.ReadAll(res.Body)\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\tlogrus.Debugf(\"Got body: %s\", body)\n\t// FIXME: Just throwing this useful information away only to try to guess later...\n\tlogrus.Debugf(\"Got content-type: %s\", res.Header.Get(\"Content-Type\"))"
                                },
                                "top_docs": [
                                        {
                                                "id": "c177956",
                                                "title": "doRequest",
                                                "text": "\n\n\tvar status status\n\tstatusValid := false\n\tif err := json.Unmarshal(body, &status); err == nil && len(status.Status) > 0 {\n\t\tstatusValid = true\n\t}\n\n\tswitch {\n\tcase res.StatusCode == http.StatusSwitchingProtocols: // FIXME?! No idea why this weird case exists in k8s.io/kubernetes/pkg/client/restclient.\n\t\tif statusValid && status.Status != \"Success\" {\n\t\t\treturn nil, errors.New(status.Message)\n\t\t}\n\tcase res.StatusCode >= http.StatusOK && res.StatusCode <= http.StatusPartialContent:\n\t\t// OK.\n\tdefault:\n\t\tif statusValid {\n\t\t\treturn nil, errors.New(status.Message)\n\t\t}\n\t\treturn nil, errors.Errorf(\"HTTP error: status code: %d (%s), body: %s\", res.StatusCode, http.StatusText(res.StatusCode), string(body))\n\t}\n\n\treturn body, nil\n}",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q181700",
                                        "text": "func (f *FormErrors) AddError(e string) {\n\tf.Errors"
                                },
                                "top_docs": [
                                        {
                                                "id": "c181700",
                                                "title": "AddError",
                                                "text": " = append(f.Errors, e)\n}",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-ccr-java": {
                "name": "CodeSearchNet-ccr-java",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q180695",
                                        "text": "public boolean offer(E o)\n    {\n        // Make a new node out of the new data element.\n        Node newNode = new Node(o);\n\n        // Check if there is already a minimum element.\n        if (minNode != null)\n        {\n            // There is already a minimum element, so add this new element to its right.\n            newNode.next = minNode.next;\n            newNode.prev = minNode;\n\n            minNode.next.prev = newNode;\n            minNode.next = newNode;\n\n            // Compare the new element with the minimum and update the minimum if neccessary.\n   "
                                },
                                "top_docs": [
                                        {
                                                "id": "c180695",
                                                "title": "FibonacciHeap.offer",
                                                "text": "         updateMinimum(newNode);\n        }\n\n        // There is not already a minimum element.\n        else\n        {\n            // Update the new element previous and next references to refer to itself so that it forms a doubly linked\n            // list with only one element. This leaves the data structure in a suitable condition for adding more\n            // elements.\n            newNode.next = newNode;\n            newNode.prev = newNode;\n\n            // Set the minimum element to be the new data element.\n            minNode = newNode;\n        }\n\n        // Increment the count of data elements in this collection.\n        size++;\n\n        // Return true to indicate that the new data element was accepted into the heap.\n        return true;\n    }",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q172805",
                                        "text": "public static ResourceDefinition getElytronKeyStoreResourceDefinition() {\n        final AttributeDefinition[] attributes = new AttributeDefinition[] {LEGACY_JSSE_CONFIG};\n        final AbstractAddStepHandler addHandler = new BasicAddHandler<KeyStore>(attributes, KEY_STORE_RUNTIME_CAPABILITY) {\n\n            @Override\n            protected BasicService.ValueSupplier<KeyStore> getValueSupplier(ServiceBuilder<KeyStore> serviceBuilder, OperationContext context, ModelNode model) throws OperationFailedException {\n                final String legacyJSSEConfig = asStringIfDefined(context, LEGACY_JSSE_CONFIG, model);\n                final InjectedValue<SecurityDomainContext> securityDomainContextInjector = new InjectedValue<>();\n                if (legacyJSSEConfig != null) {\n                    serviceBuilder.addDependency(SecurityDomainService.SERVICE_NAME.append(legacyJSSEConfig), SecurityDomainContext.class, securityDomainContextInjector);\n                }\n\n                return () -> {\n                    final SecurityDomainContext domainContext = securityDomainContextInjector.getValue();\n                    final JSSESecurityDomain jsseDomain = domainContext.getJSSE();\n                 "
                                },
                                "top_docs": [
                                        {
                                                "id": "c172805",
                                                "title": "ElytronIntegrationResourceDefinitions.getElytronKeyStoreResourceDefinition",
                                                "text": "   if (jsseDomain == null) {\n                        throw SecurityLogger.ROOT_LOGGER.unableToLocateJSSEConfig(legacyJSSEConfig);\n                    }\n                    final KeyStore keyStore = jsseDomain.getKeyStore();\n                    if (keyStore == null) {\n                        throw SecurityLogger.ROOT_LOGGER.unableToLocateComponentInJSSEDomain(\"KeyStore\", legacyJSSEConfig);\n                    }\n                    return keyStore;\n                };\n            }\n        };\n\n        return new BasicResourceDefinition(Constants.ELYTRON_KEY_STORE, addHandler, attributes, KEY_STORE_RUNTIME_CAPABILITY);\n    }",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q171995",
                                        "text": "public static int validateAndComputeLength(final DirectBufferVector[] vectors)\n    {\n        int messageLength = 0;\n        for (final DirectBufferVector vector : vectors)\n        {\n       "
                                },
                                "top_docs": [
                                        {
                                                "id": "c171995",
                                                "title": "DirectBufferVector.validateAndComputeLength",
                                                "text": "     vector.validate();\n            messageLength += vector.length;\n\n            if (messageLength < 0)\n            {\n                throw new IllegalStateException(\"length overflow: \" + Arrays.toString(vectors));\n            }\n        }\n\n        return messageLength;\n    }",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-ccr-javascript": {
                "name": "CodeSearchNet-ccr-javascript",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q63335",
                                        "text": "function allSettled(promises) {\n    \"use strict\";\n    const wrappedPromises"
                                },
                                "top_docs": [
                                        {
                                                "id": "c63335",
                                                "title": "allSettled",
                                                "text": " = promises.map((curPromise) => curPromise.reflect());\n    return Promise.all(wrappedPromises);\n}",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q64393",
                                        "text": "function subRegister(obj, name) {\n  var res;\n  res = isPrimitive(obj[name]) ? {} : obj[name];\n  return obj[name] ="
                                },
                                "top_docs": [
                                        {
                                                "id": "c64393",
                                                "title": "subRegister",
                                                "text": " mixable(res).mixin(proto, 'register', 'extend');\n}",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q62103",
                                        "text": "function resetToMove(contextControl) {\n    var left = contextControl.find('.source .transfer-group')\n    var right = contextControl.find('.target .transfer-group')\n    var textLeft = contextControl.find('.source .transfer-header span.num')\n    var textRight = contextControl.find('.target .transfer-header span.num')\n    var header ="
                                },
                                "top_docs": [
                                        {
                                                "id": "c62103",
                                                "title": "resetToMove",
                                                "text": " contextControl.find('.transfer-header input')\n\n    $(left).html(elemLeft)\n    $(right).html(elemRight)\n\n    $(textLeft).text(elemLeftNum)\n    $(textRight).text(elemRightNum)\n\n    $(header).prop('disabled', false)\n  }",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-ccr-ruby": {
                "name": "CodeSearchNet-ccr-ruby",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q26791",
                                        "text": "def connect\n      start_time = Time.now\n      retries    = 0\n      close\n\n      # Number of times to try\n      begin\n        connect_to_server(servers, policy)\n        logger.info(message: \"Connected to #{address}\", duration: (Time.now - start_time) * 1000) if respond_to?(:logger)\n      rescue ConnectionFailure, ConnectionTimeout => exception\n        cause = exception.is_a?(ConnectionTimeout) ? exception : exception.cause\n        # Retry-able?\n        if self.class.reconnect_on_errors.include?(cause.class) && (retries < connect_retry_count.to_i)\n          retries += 1\n          logger.warn \"#connect Failed to connect to any of #{servers.join(',')}. Sleeping:#{connect_retry_interval}s. Retry: #{retries}\" if respond_to?(:logger)\n          sleep(connect_retry_interval)\n          retry\n        else\n"
                                },
                                "top_docs": [
                                        {
                                                "id": "c26791",
                                                "title": "Net.TCPClient.connect",
                                                "text": "          message = \"#connect Failed to connect to any of #{servers.join(',')} after #{retries} retries. #{exception.class}: #{exception.message}\"\n          logger.benchmark_error(message, exception: exception, duration: (Time.now - start_time)) if respond_to?(:logger)\n          raise ConnectionFailure.new(message, address.to_s, cause)\n        end\n      end\n    end",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q26523",
                                        "text": "def ensure_fork_commits(owner, repo, parent_owner, parent_repo)\n\n      currepo = ensure_repo(owner, repo)\n\n      if currepo.nil?\n        warn \"Could not find repo #{owner}/#{repo}\"\n        return\n      end\n\n      parent = ensure_repo(parent_owner, parent_repo)\n\n      if parent.nil?\n        warn \"Could not find repo #{parent_owner}/#{parent_repo}, parent of #{owner}/#{repo}\"\n        return\n      end\n\n      strategy = case\n                   when config(:fork_commits).match(/all/i)\n                     :all\n                   when config(:fork_commits).match(/fork_point/i)\n                     :fork_point\n                   when config(:fork_commits).match(/none/i)\n                     :none\n                   else\n                     :fork_point\n                 end\n\n      fork_commit = ensure_fork_point(owner, repo)\n\n      if fork_commit.nil? or fork_commit.empty?\n        warn \"Could not find fork commit for repo #{owner}/#{repo}. Retrieving all commits.\"\n        return ensure_commits(owner, repo, fork_all: true)\n      end\n\n      debug \"Retrieving commits for fork #{owner}/#{repo}: strategy is #{strategy}\"\n      return if strategy == :none\n\n      if strategy == :fork_point\n        # Retrieve commits up to fork point (fork_commit strategy)\n        info \"Retrieving commits for #{owner}/#{repo} until fork commit #{fork_commit[:sha]}\"\n        master_branch = retrieve_default_branch(parent_owner, parent_repo)\n        return if master_branch.nil?\n\n        sha   = master_branch\n        found = false\n        while not found\n          commits = retrieve_commits(repo, sha, owner, 1)\n\n          # This means that we retrieved no commits\n          if commits.size == 0\n            break\n          end\n\n          # This means we retrieved the last page again\n          if commits.size == 1 and commits[0]['sha'] == sha\n            break\n          end\n\n          for c in commits\n            ensure_commit(repo, c['sha'], owner)\n            sha = c['sha']\n            if c['sha'] == fork_commit[:sha]\n              found = true\n              break\n"
                                },
                                "top_docs": [
                                        {
                                                "id": "c26523",
                                                "title": "GHTorrent.Mirror.ensure_fork_commits",
                                                "text": "            end\n          end\n        end\n      end\n\n      if strategy == :all\n\n        shared_commit = db[:commits].first(:sha => fork_commit)\n        copied        = 0\n        to_copy = db.from(:project_commits, :commits).\\\n                  where(Sequel.qualify('project_commits', 'commit_id') => Sequel.qualify('commits', 'id')).\\\n                  where(Sequel.qualify('project_commits', 'project_id') => parent[:id]).\\\n                  where('commits.created_at < ?', shared_commit[:created_at]).\\\n                  select(Sequel.qualify('commits','id'))\n\n        to_copy.each do |c|\n          copied += 1\n          begin\n            db[:project_commits].insert(\n                :project_id => currepo[:id],\n                :commit_id  => c[:id]\n            )\n            debug \"Copied commit #{c[:sha]} #{parent_owner}/#{parent_repo} -> #{owner}/#{repo} (#{copied} total)\"\n          rescue StandardError => e\n            warn \"Could not copy commit #{c[:sha]} #{parent_owner}/#{parent_repo} -> #{owner}/#{repo} : #{e.message}\"\n          end\n        end\n        info \"Finished copying commits from #{parent_owner}/#{parent_repo} -> #{owner}/#{repo}: #{copied} total\"\n      end\n\n    end",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q27341",
                                        "text": "def replace(template)\n            template = template.to_mixml_template\n\n            each_node do |node|\n               "
                                },
                                "top_docs": [
                                        {
                                                "id": "c27341",
                                                "title": "Mixml.Selection.replace",
                                                "text": " value = template.evaluate(node)\n                node.replace(value)\n            end\n        end",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-ccr-python": {
                "name": "CodeSearchNet-ccr-python",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q268554",
                                        "text": "def __ginibre_matrix(nrow, ncol=None, seed=None):\n    \"\"\"\n    Return a normally distributed complex random matrix.\n\n    Args:\n        nrow (int): number of rows in output matrix.\n        ncol (int): number of columns in output matrix.\n        seed (int): Optional. To set a random seed.\n    Returns:\n        ndarray: A complex rectangular matrix where each real and imaginary\n       "
                                },
                                "top_docs": [
                                        {
                                                "id": "c268554",
                                                "title": "__ginibre_matrix",
                                                "text": "     entry is sampled from the normal distribution.\n    \"\"\"\n    if ncol is None:\n        ncol = nrow\n    if seed is not None:\n        np.random.seed(seed)\n    G = np.random.normal(size=(nrow, ncol)) + \\\n        np.random.normal(size=(nrow, ncol)) * 1j\n    return G",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q267552",
                                        "text": "def list_slot_differences_slot(\n            self, resource_group_name, name, slot, target_slot, preserve_vnet, custom_headers=None, raw=False, **operation_config):\n        \"\"\"Get the difference in configuration settings between two web app slots.\n\n        Get the difference in configuration settings between two web app slots.\n\n        :param resource_group_name: Name of the resource group to which the\n         resource belongs.\n        :type resource_group_name: str\n        :param name: Name of the app.\n        :type name: str\n        :param slot: Name of the source slot. If a slot is not specified, the\n         production slot is used as the source slot.\n        :type slot: str\n        :param target_slot: Destination deployment slot during swap operation.\n        :type target_slot: str\n        :param preserve_vnet: <code>true</code> to preserve Virtual Network to\n         the slot during swap; otherwise, <code>false</code>.\n        :type preserve_vnet: bool\n        :param dict custom_headers: headers that will be added to the request\n        :param bool raw: returns the direct response alongside the\n         deserialized response\n        :param operation_config: :ref:`Operation configuration\n         overrides<msrest:optionsforoperations>`.\n        :return: An iterator like instance of SlotDifference\n        :rtype:\n         ~azure.mgmt.web.models.SlotDifferencePaged[~azure.mgmt.web.models.SlotDifference]\n        :raises:\n         :class:`DefaultErrorResponseException<azure.mgmt.web.models.DefaultErrorResponseException>`\n        \"\"\"\n        slot_swap_entity = models.CsmSlotEntity(target_slot=target_slot, preserve_vnet=preserve_vnet)\n\n        def internal_paging(next_link=None, raw=False):\n\n            if not next_link:\n                # Construct URL\n                url = self.list_slot_differences_slot.metadata['url']\n                path_format_arguments = {\n                    'resourceGroupName': self._serialize.url(\"resource_group_name\", resource_group_name, 'str', max_length=90, min_length=1, pattern=r'^[-\\w\\._\\(\\)]+[^\\.]$'),\n                    'name': self._serialize.url(\"name\", name, 'str'),\n                    'slot': self._serialize.url(\"slot\", slot, 'str'),\n                    'subscriptionId': self._serialize.url(\"self.config.subscription_id\", self.config.subscription_id, 'str')\n                }\n                url = self._client.format_url(url, **path_format_arguments)\n\n                # Construct parameters\n                query_parameters = {}\n                query_parameters['api-version'] = self._serialize.query(\"self.api_version\", self.api_version, 'str')\n\n            else:\n       "
                                },
                                "top_docs": [
                                        {
                                                "id": "c267552",
                                                "title": "WebAppsOperations.list_slot_differences_slot",
                                                "text": "         url = next_link\n                query_parameters = {}\n\n            # Construct headers\n            header_parameters = {}\n            header_parameters['Accept'] = 'application/json'\n            header_parameters['Content-Type'] = 'application/json; charset=utf-8'\n            if self.config.generate_client_request_id:\n                header_parameters['x-ms-client-request-id'] = str(uuid.uuid1())\n            if custom_headers:\n                header_parameters.update(custom_headers)\n            if self.config.accept_language is not None:\n                header_parameters['accept-language'] = self._serialize.header(\"self.config.accept_language\", self.config.accept_language, 'str')\n\n            # Construct body\n            body_content = self._serialize.body(slot_swap_entity, 'CsmSlotEntity')\n\n            # Construct and send request\n            request = self._client.post(url, query_parameters, header_parameters, body_content)\n            response = self._client.send(request, stream=False, **operation_config)\n\n            if response.status_code not in [200]:\n                raise models.DefaultErrorResponseException(self._deserialize, response)\n\n            return response\n\n        # Deserialize response\n        deserialized = models.SlotDifferencePaged(internal_paging, self._deserialize.dependencies)\n\n        if raw:\n            header_dict = {}\n            client_raw_response = models.SlotDifferencePaged(internal_paging, self._deserialize.dependencies, header_dict)\n            return client_raw_response\n\n        return deserialized",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q279365",
                                        "text": "def options(self, parser, env):\n        \"\"\"\n        Register command-line options.\n        \"\"\"\n        parser.add_option(\"--processes\", action=\"store\",\n                          default=env.get('NOSE_PROCESSES', 0),\n                          dest=\"multiprocess_workers\",\n                          metavar=\"NUM\",\n                          help=\"Spread test run among this many processes. \"\n                          \"Set a number equal to the number of processors \"\n                          \"or cores in your machine for best results. \"\n                          \"[NOSE_PROCESSES]\")\n        parser.add_option(\"--process-timeout\", action=\"store\",\n                          default=env.get('NOSE_PROCESS_TIMEOUT', 10),\n                          dest=\"multiprocess_timeout\",\n                          metavar=\"SECONDS\",\n                          help=\"Set timeout for return of results from each \"\n                  "
                                },
                                "top_docs": [
                                        {
                                                "id": "c279365",
                                                "title": "MultiProcess.options",
                                                "text": "        \"test runner process. [NOSE_PROCESS_TIMEOUT]\")\n        parser.add_option(\"--process-restartworker\", action=\"store_true\",\n                          default=env.get('NOSE_PROCESS_RESTARTWORKER', False),\n                          dest=\"multiprocess_restartworker\",\n                          help=\"If set, will restart each worker process once\"\n                          \" their tests are done, this helps control memory \"\n                          \"leaks from killing the system. \"\n                          \"[NOSE_PROCESS_RESTARTWORKER]\")",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        },
        "CodeSearchNet-ccr-php": {
                "name": "CodeSearchNet-ccr-php",
                "description": "代码信息检索任务",
                "samples": [
                        {
                                "query": {
                                        "id": "q264196",
                                        "text": "public function disableFilter(string $filter)\n    {\n        if (\\in_array($filter, $this->disabledFilters, true)) {"
                                },
                                "top_docs": [
                                        {
                                                "id": "c264196",
                                                "title": "FiltersTrait.disableFilter",
                                                "text": "\n            return;\n        }\n\n        $this->getFilterCollection()->disable($filter);\n\n        $this->disabledFilters[] = $filter;\n    }",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q261089",
                                        "text": "private function createParserWith(StreamInterface $body)\n    {\n        $class = NullParser::class;\n        foreach (self::$parsers as $parser => $contentTypes) {\n    "
                                },
                                "top_docs": [
                                        {
                                                "id": "c261089",
                                                "title": "BodyParser.createParserWith",
                                                "text": "        foreach ($contentTypes as $contentType) {\n                if (stripos($this->contentType, $contentType) !== false) {\n                    $class = $parser;\n                }\n            }\n        }\n        return new $class($body);\n    }",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        },
                        {
                                "query": {
                                        "id": "q265296",
                                        "text": "public function addSetting(ISetting $setting, $cachable = true)\n    {\n        $this->settings[] = $setting;\n\n        if (true === $cachable) {\n "
                                },
                                "top_docs": [
                                        {
                                                "id": "c265296",
                                                "title": "Result.addSetting",
                                                "text": "           $this->cachable_settings[] = $setting;\n        }\n\n        return $this;\n    }",
                                                "language": "",
                                                "score": 1
                                        }
                                ]
                        }
                ]
        }
};

        // 显示任务样本
        function showTaskSamples(taskName) {
            console.log('Showing samples for task:', taskName);

            // 更新卡片状态
            document.querySelectorAll('.task-card').forEach(card => {
                card.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            const task = taskData[taskName];
            const container = document.getElementById('samples-container');

            if (!task) {
                console.error('Task not found:', taskName);
                return;
            }

            // 创建DOM元素而不是字符串拼接
            const samplesDiv = document.createElement('div');

            // 添加标题
            const headerDiv = document.createElement('div');
            headerDiv.className = 'samples-header';
            headerDiv.innerHTML = '<h2>' + escapeHtml(task.name) + '</h2><p>' + escapeHtml(task.description) + '</p>';
            samplesDiv.appendChild(headerDiv);

            // 添加样本
            task.samples.forEach((sample, index) => {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'sample-row';

                // 查询块
                const queryBlock = createCodeBlock(
                    '🔍 查询 ' + (index + 1),
                    sample.query.text,
                    'text',
                    'query-header'
                );
                rowDiv.appendChild(queryBlock);

                // 文档块
                sample.top_docs.forEach((doc, docIndex) => {
                    const docBlock = createCodeBlock(
                        '📄 排名 ' + (docIndex + 1) + ' (' + (doc.language || 'text') + ') - 分数: ' + doc.score,
                        doc.text,
                        doc.language || 'text',
                        'rank-' + (docIndex + 1)
                    );
                    rowDiv.appendChild(docBlock);
                });

                samplesDiv.appendChild(rowDiv);
            });

            container.innerHTML = '';
            container.appendChild(samplesDiv);
            container.classList.add('active');

            console.log('任务样本显示完成');
        }

        // 创建代码块元素
        function createCodeBlock(title, content, language, headerClass) {
            const blockDiv = document.createElement('div');
            blockDiv.className = 'code-block';

            const headerDiv = document.createElement('div');
            headerDiv.className = 'code-header ' + headerClass;

            const titleSpan = document.createElement('span');
            titleSpan.textContent = title;
            headerDiv.appendChild(titleSpan);

            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-btn';
            copyBtn.textContent = '复制';
            copyBtn.onclick = function() { copyText(this, content); };
            headerDiv.appendChild(copyBtn);

            const contentDiv = document.createElement('div');
            contentDiv.className = 'code-content';

            const pre = document.createElement('pre');
            pre.style.whiteSpace = 'pre-wrap';
            pre.style.wordWrap = 'break-word';
            pre.style.backgroundColor = '#f8f9fa';
            pre.style.padding = '10px';
            pre.style.borderRadius = '4px';
            pre.style.fontSize = '0.9em';
            pre.style.fontFamily = 'Monaco, Consolas, "Courier New", monospace';
            pre.textContent = content;
            contentDiv.appendChild(pre);

            blockDiv.appendChild(headerDiv);
            blockDiv.appendChild(contentDiv);

            return blockDiv;
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 复制文本
        function copyText(button, text) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = 'rgba(40, 167, 69, 0.8)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(255,255,255,0.2)';
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }
    </script>
</body>
</html>