"""
生成COIR数据集完整可视化
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from benchmarks.coir.coir_utils import CoirUtils
import argparse

def main():
    """生成COIR数据集可视化"""
    parser = argparse.ArgumentParser(description="生成COIR数据集可视化HTML")
    parser.add_argument("--tasks", nargs="+",
                       default=None,
                       help="要处理的任务列表，默认处理所有任务")
    parser.add_argument("--samples", type=int, default=3,
                       help="每个任务采样的查询数量")
    parser.add_argument("--output", default="coir_data_visualization.html",
                       help="输出HTML文件路径")
    
    args = parser.parse_args()
    
    try:
        print("🚀 开始生成COIR数据集可视化...")
        if args.tasks:
            print(f"📋 任务列表: {', '.join(args.tasks)}")
        else:
            print("📋 使用默认任务列表（所有任务）")
        print(f"🎯 每个任务采样数量: {args.samples}")
        
        # 创建数据可视化
        output_file = CoirUtils.create_data_visualization(
            tasks=args.tasks,
            num_samples=args.samples,
            output_path=args.output
        )
        
        print(f"✅ 可视化生成完成！")
        print(f"📄 HTML文件: {output_file}")
        print("💡 请在浏览器中打开该文件查看可视化效果")
        
        # 提供一些使用建议
        print("\n📝 使用说明:")
        print("- 点击任务卡片切换查看不同任务的样本")
        print("- 每行显示1个查询和3个按分数排序的代码块")
        print("- 每个代码块都有复制按钮")
        print("- 代码支持语法高亮和行号显示")
        print("- 页面响应式设计，支持移动设备")
        
    except Exception as e:
        print(f"❌ 生成失败:", e)
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
