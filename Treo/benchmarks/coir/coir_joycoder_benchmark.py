import os
import json
import hashlib
from typing import Dict, List, Tuple
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from coir.data_loader import get_tasks
from coir.beir.retrieval.search import BaseSearch
from coir.beir.retrieval.evaluation import Eva<PERSON>ate<PERSON><PERSON><PERSON><PERSON>

from benchmarks.benchmark_model import BenchmarkModel
from benchmarks.coir.coir_utils import CoirUtils
from api.codebase import CodeBaseApi
from api.schema import *
from utils.logger import logger

try:
    import xxhash
    XXHASH_AVAILABLE = True
except ImportError:
    XXHASH_AVAILABLE = False

class CoirJoycoderBenchmarkModel(BenchmarkModel, BaseSearch):
    def __init__(self, base_service: CodeBaseApi, output_dir: str, tasks: List[str] = ["codetrans-dl","stackoverflow-qa","apps","codefeedback-mt","codefeedback-st","codetrans-contest","synthetic-text2sql","cosqa","codesearchnet","codesearchnet-ccr"], concurrent_workers: int = 1):
        super().__init__(base_service, output_dir)
        self.base_service = base_service
        self.tasks = get_tasks(tasks=tasks)
        self.corpushash2projectid = {}
        self.concurrent_workers = concurrent_workers

        self.sep = " "
        self.task_name = None

    def _generate_create_project_params(self, task_name: str) -> Dict[str, str]:
        return {
            "repo_url": f"https://github.com/test/repo_{task_name}.git",
            "username": "coir_benchmark",
            "branch": "main",
            "source": "remote"
        }
    
    def _prepare_merkle_tree(self, corpus: Dict[str, Dict[str, str]]) -> Tuple[List[str], List[str], MerkleTreeNode]:
        doc_paths = []
        for doc_id, doc_content in corpus.items():
            file_path = f"doc_{doc_id}"
            if doc_content.get('language'):
                file_path += f".{doc_content['language']}"
            else:
                file_path += ".txt"
            doc_paths.append(file_path) 

        doc_contents = []
        for doc_id, doc_content in corpus.items():
            doc_contents.append((doc_content["title"] + self.sep + doc_content["text"]).strip() if "title" in doc_content else doc_content["text"].strip())

        return doc_paths, doc_contents, MerkleTreeNode(
            path=".",
            is_file=False,
            hash="",
            children=[
                MerkleTreeNode(
                    path=doc_path,
                    is_file=True,
                    hash=hashlib.sha256(doc_contents[i].encode('utf-8')).hexdigest()
                )
                for i, doc_path in enumerate(doc_paths)
            ]
        )

    def _retrieve_single_query(self, project_id: str, query_id: str, query: str, top_k: int) -> Tuple[str, Dict[str, float]]:
        """
        执行单个查询的检索

        Args:
            project_id: 项目ID
            query_id: 查询ID
            query: 查询内容
            top_k: 返回结果数量

        Returns:
            (query_id, {doc_id: score})
        """
        try:
            retrieve_docs = self.base_service.retrieve(
                project_id,
                repo_url=f"https://github.com/test/repo_{self.task_name}.git",
                query=query,
                branch='main',
                username='coir_benchmark',
                max_context_chunks=top_k
            )

            query_results = {}
            for doc_path, score in retrieve_docs.items():
                doc_id = doc_path.split("/")[-1].split(".")[0][4:]
                query_results[doc_id] = score

            return query_id, query_results

        except Exception as e:
            logger.error(f"Error retrieving query {query_id}:", e)
            return query_id, {}

    def search(self, corpus: Dict[str, Dict[str, str]], queries: Dict[str, str], top_k: int, *args, **kwargs) -> Dict[str, Dict[str, float]]:
        """
        并发执行多个查询的检索

        Args:
            corpus: 文档语料库
            queries: 查询字典 {query_id: query_text}
            top_k: 每个查询返回的结果数量
            *args: 额外参数
            **kwargs: 额外关键字参数，支持 concurrent_workers 参数覆盖默认并发数

        Returns:
            检索结果字典 {query_id: {doc_id: score}}
        """
        project_id = self.corpushash2projectid[CoirUtils.generate_corpus_hash(corpus)]
        query_ids = list(queries.keys())

        # 从kwargs中获取并发数，如果没有则使用默认值
        concurrent_workers = kwargs.get('concurrent_workers', self.concurrent_workers)

        logger.info(f"Starting concurrent search with {concurrent_workers} workers for {len(query_ids)} queries")

        results = defaultdict(dict)

        # 使用线程池执行并发检索
        with ThreadPoolExecutor(max_workers=concurrent_workers) as executor:
            # 提交所有查询任务
            future_to_query = {
                executor.submit(self._retrieve_single_query, project_id, query_id, queries[query_id], top_k): query_id
                for query_id in query_ids
            }

            # 收集结果
            completed_count = 0
            for future in as_completed(future_to_query):
                query_id, query_results = future.result()
                results[query_id] = query_results
                completed_count += 1

                if completed_count % 10 == 0:  # 每完成10个查询记录一次进度
                    logger.info(f"Completed {completed_count}/{len(query_ids)} queries")

        logger.info(f"Concurrent search completed for {len(query_ids)} queries")
        return results

    def init_dataset(self):
        logger.info("⏱️ Starting dataset initialization...")

        for task_name, task_data in self.tasks.items():
            logger.info(f"Initializing dataset for {task_name}...")
            corpus_hash = CoirUtils.generate_corpus_hash(task_data[0])
            
            # 获取或创建项目
            create_project_response: CreateProjectResponse = self.base_service.create_project(**self._generate_create_project_params(task_name))
            
            self.corpushash2projectid[corpus_hash] = create_project_response.project_id

            if not create_project_response.is_new:
                continue

            # 上传项目结构
            corpus = task_data[0]

            doc_paths, doc_contens, merkle_node = self._prepare_merkle_tree(corpus)

            self.base_service.load_context_files(create_project_response.project_id, 
                                                merkle_node, 
                                                [
                                                    FileUpload(file_path=doc_path, file_content=doc_content, file_hash=hashlib.sha256(doc_content.encode('utf-8')).hexdigest())
                                                    for doc_path, doc_content in zip(doc_paths, doc_contens)
                        ])

        logger.info("✅ Dataset initialization completed")
        
    def evaluate(self, top_k: int = 5, k_values: List[int] = [1, 3, 5, 10], max_query_count: int = 2000, *args, **kwargs) -> Dict[str, float]:
        results = {}

        retriever = EvaluateRetrieval(self, k_values=k_values)

        logger.info("Starting evaluation...")
        for task_name, task_data in self.tasks.items():
            output_file = os.path.join(self.output_dir, f"{task_name}.json")

            # Check if the output file already exists
            if os.path.exists(output_file):
                print(f"Results for {task_name} already exist. Skipping task.")
                continue
  
            corpus, queries, qrels = task_data
            self.task_name = task_name

            if len(queries) > max_query_count:
                new_queries = {}
                i = 0
                for k, v in queries.items():
                    if i >= max_query_count:
                        break
                    new_queries[k] = v
                    i += 1
                queries = new_queries

            # Retrieve results
            task_results = retriever.retrieve(corpus, queries)
            
            # Evaluate results
            ndcg, map, recall, precision = retriever.evaluate(qrels, task_results, retriever.k_values)
            metrics = {
                "NDCG": ndcg,
                "MAP": map,
                "Recall": recall,
                "Precision": precision
            }

            # Save results
            os.makedirs(self.output_dir, exist_ok=True)
            with open(output_file, 'w') as json_file:
                json.dump({"metrics": metrics}, json_file, indent=4)

            logger.info(f"Results for {task_name} saved to {self.output_dir}")
            results[task_name] = metrics

        return results

