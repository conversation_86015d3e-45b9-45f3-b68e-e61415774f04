#!/usr/bin/env python3
"""
自动从GitHub克隆项目
在克隆前检查输出目录中是否已经存在项目，若存在则跳过
若项目超时未能克隆成功，则输出警告信息

主函数入口参数:
--path: 仓库链接列表文件路径（required）
--output: 仓库克隆目录（required）
--timeout: 克隆超时时间，默认300秒
--concurrent: 并发克隆数量，默认3
--depth: 克隆深度，默认1（浅克隆）
"""

import os
import sys
import json
import argparse
import subprocess
import threading
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed


class RepoDownloader:
    """仓库下载器"""

    def __init__(self, output_dir: str, timeout: int = 300, concurrent: int = 3, depth: int = 1):
        self.output_dir = Path(output_dir)
        self.timeout = timeout
        self.concurrent = concurrent
        self.depth = depth
        self.stats = {
            'total': 0,
            'skipped': 0,
            'success': 0,
            'failed': 0,
            'timeout': 0
        }

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def parse_repo_url(self, repo_url: str) -> Dict[str, str]:
        """解析仓库URL，提取仓库信息"""
        try:
            # 处理不同格式的URL
            if repo_url.endswith('.git'):
                repo_url = repo_url[:-4]

            parsed = urlparse(repo_url)
            path_parts = parsed.path.strip('/').split('/')

            if len(path_parts) >= 2:
                owner = path_parts[0]
                repo_name = path_parts[1]
                return {
                    'owner': owner,
                    'name': repo_name,
                    'full_name': f"{owner}/{repo_name}",
                    'clone_url': f"https://github.com/{owner}/{repo_name}.git"
                }
            else:
                raise ValueError(f"Invalid repository URL format: {repo_url}")

        except Exception as e:
            raise ValueError(f"Failed to parse repository URL '{repo_url}': {e}")

    def get_repo_local_path(self, repo_info: Dict[str, str], language: Optional[str] = None) -> Path:
        """获取仓库的本地路径"""
        if language:
            return self.output_dir / language / repo_info['name']
        else:
            return self.output_dir / repo_info['owner'] / repo_info['name']

    def is_repo_exists(self, local_path: Path) -> bool:
        """检查仓库是否已存在"""
        return local_path.exists() and (local_path / '.git').exists()

    def clone_repository(self, repo_info: Dict[str, str], local_path: Path) -> Dict[str, Any]:
        """克隆单个仓库"""
        result = {
            'repo': repo_info['full_name'],
            'url': repo_info['clone_url'],
            'local_path': str(local_path),
            'success': False,
            'skipped': False,
            'error': None,
            'duration': 0
        }

        start_time = time.time()

        try:
            # 检查是否已存在
            if self.is_repo_exists(local_path):
                print(f"⏭️  Skipping {repo_info['full_name']} (already exists)")
                result['skipped'] = True
                self.stats['skipped'] += 1
                return result

            # 确保父目录存在
            local_path.parent.mkdir(parents=True, exist_ok=True)

            # 构建git clone命令
            cmd = [
                'git', 'clone',
                '--depth', str(self.depth),
                '--single-branch',
                repo_info['clone_url'],
                str(local_path)
            ]

            print(f"📥 Cloning {repo_info['full_name']}...")

            # 执行克隆命令
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.timeout
            )

            if process.returncode == 0:
                duration = time.time() - start_time
                result['success'] = True
                result['duration'] = duration
                self.stats['success'] += 1
                print(f"✅ Successfully cloned {repo_info['full_name']} ({duration:.1f}s)")
            else:
                error_msg = process.stderr.strip() or process.stdout.strip()
                result['error'] = f"Git clone failed: {error_msg}"
                self.stats['failed'] += 1
                print(f"❌ Failed to clone {repo_info['full_name']}: {error_msg}")

        except subprocess.TimeoutExpired:
            result['error'] = f"Clone timeout after {self.timeout} seconds"
            self.stats['timeout'] += 1
            print(f"⏰ Timeout cloning {repo_info['full_name']} (>{self.timeout}s)")

            # 清理可能的部分克隆
            if local_path.exists():
                try:
                    subprocess.run(['rm', '-rf', str(local_path)], check=True)
                except:
                    pass

        except Exception as e:
            result['error'] = str(e)
            self.stats['failed'] += 1
            print(f"❌ Error cloning {repo_info['full_name']}: {e}")

        return result

    def download_repos_from_list(self, repo_list: List[str], language: Optional[str] = None) -> List[Dict[str, Any]]:
        """从仓库列表下载所有仓库"""
        self.stats['total'] = len(repo_list)
        results = []

        print(f"🚀 Starting download of {len(repo_list)} repositories")
        print(f"   Output directory: {self.output_dir}")
        print(f"   Concurrent downloads: {self.concurrent}")
        print(f"   Timeout: {self.timeout}s")
        print(f"   Clone depth: {self.depth}")
        if language:
            print(f"   Language category: {language}")
        print()

        # 解析所有仓库URL
        repo_tasks = []
        for repo_url in repo_list:
            try:
                repo_info = self.parse_repo_url(repo_url.strip())
                local_path = self.get_repo_local_path(repo_info, language)
                repo_tasks.append((repo_info, local_path))
            except ValueError as e:
                print(f"❌ {e}")
                results.append({
                    'repo': repo_url,
                    'success': False,
                    'error': str(e)
                })
                self.stats['failed'] += 1

        # 并发下载
        with ThreadPoolExecutor(max_workers=self.concurrent) as executor:
            future_to_repo = {
                executor.submit(self.clone_repository, repo_info, local_path): (repo_info, local_path)
                for repo_info, local_path in repo_tasks
            }

            for future in as_completed(future_to_repo):
                result = future.result()
                results.append(result)

        return results

    def print_summary(self):
        """打印下载统计摘要"""
        print("\n" + "="*60)
        print("📊 Download Summary")
        print("="*60)
        print(f"Total repositories: {self.stats['total']}")
        print(f"✅ Successfully cloned: {self.stats['success']}")
        print(f"⏭️  Skipped (already exists): {self.stats['skipped']}")
        print(f"❌ Failed: {self.stats['failed']}")
        print(f"⏰ Timeout: {self.stats['timeout']}")

        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] / self.stats['total']) * 100
            print(f"📈 Success rate: {success_rate:.1f}%")

        print("="*60)


def load_repo_list(file_path: str) -> List[str]:
    """从文件加载仓库列表"""
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"Repository list file not found: {file_path}")

    repos = []

    try:
        if file_path.suffix.lower() == '.json':
            # JSON格式文件
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if isinstance(data, list):
                # 简单的URL列表
                repos = [item if isinstance(item, str) else item.get('url', '') for item in data]
            elif isinstance(data, dict):
                # 按语言分类的格式
                for language, repo_list in data.items():
                    repos.extend(repo_list)
            else:
                raise ValueError("Invalid JSON format")

        else:
            # 文本格式文件，每行一个URL
            with open(file_path, 'r', encoding='utf-8') as f:
                repos = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]

    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON format in {file_path}: {e}")
    except Exception as e:
        raise ValueError(f"Failed to read repository list from {file_path}: {e}")

    # 过滤空URL
    repos = [repo for repo in repos if repo]

    if not repos:
        raise ValueError(f"No valid repository URLs found in {file_path}")

    return repos


def save_results(results: List[Dict[str, Any]], output_file: str):
    """保存下载结果到文件"""
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"📄 Results saved to: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="自动从GitHub克隆仓库",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python download_repos.py --path repos.txt --output ./repos
  python download_repos.py --path repos.json --output ./repos --timeout 600 --concurrent 5
  python download_repos.py --path repos.txt --output ./repos --language python --depth 10

支持的仓库列表格式:
  1. 文本文件 (.txt): 每行一个仓库URL
  2. JSON文件 (.json): URL数组或按语言分类的对象

仓库URL格式:
  - https://github.com/owner/repo
  - https://github.com/owner/repo.git
  - github.com/owner/repo
        """
    )

    parser.add_argument(
        '--path',
        required=True,
        help='仓库链接列表文件路径 (支持 .txt 和 .json 格式)'
    )

    parser.add_argument(
        '--output',
        required=True,
        help='仓库克隆输出目录'
    )

    parser.add_argument(
        '--timeout',
        type=int,
        default=300,
        help='单个仓库克隆超时时间（秒），默认300秒'
    )

    parser.add_argument(
        '--concurrent',
        type=int,
        default=3,
        help='并发克隆数量，默认3个'
    )

    parser.add_argument(
        '--depth',
        type=int,
        default=1,
        help='Git克隆深度，默认1（浅克隆）'
    )

    parser.add_argument(
        '--language',
        help='语言分类目录名（可选）'
    )

    parser.add_argument(
        '--results',
        help='保存下载结果的JSON文件路径（可选）'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅显示将要下载的仓库，不实际执行克隆'
    )

    args = parser.parse_args()

    try:
        # 加载仓库列表
        print(f"📋 Loading repository list from: {args.path}")
        repo_list = load_repo_list(args.path)
        print(f"📊 Found {len(repo_list)} repositories")

        if args.dry_run:
            print("\n🔍 Dry run mode - repositories to be cloned:")
            for i, repo_url in enumerate(repo_list, 1):
                print(f"  {i:3d}. {repo_url}")
            print(f"\nTotal: {len(repo_list)} repositories")
            return

        # 创建下载器
        downloader = RepoDownloader(
            output_dir=args.output,
            timeout=args.timeout,
            concurrent=args.concurrent,
            depth=args.depth
        )

        # 开始下载
        results = downloader.download_repos_from_list(repo_list, args.language)

        # 显示统计信息
        downloader.print_summary()

        # 保存结果
        if args.results:
            save_results(results, args.results)

        # 检查是否有失败的下载
        failed_count = sum(1 for r in results if not r.get('success', False) and not r.get('skipped', False))
        if failed_count > 0:
            print(f"\n⚠️  {failed_count} repositories failed to download. Check the output above for details.")
            sys.exit(1)
        else:
            print(f"\n🎉 All repositories processed successfully!")

    except KeyboardInterrupt:
        print("\n❌ Download interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
