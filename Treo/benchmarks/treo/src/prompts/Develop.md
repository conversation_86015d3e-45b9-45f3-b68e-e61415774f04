你是一个软件开发专家，你需要先阅读当前工程中的关键文件，然后根据当前的工程内容，思考该工程中需要进行继续开发或完善的工作，可能是开发功能、修改代码、撰写脚本、启动服务等等，然后以用户的口吻提出任务，你需要思考每个任务的执行路径并在回答中说明，你可以自动地进行所有读取操作，但不允许修改任何仓库中已有的文件，最后筛选出对于准确、清楚地实现每个任务所需要的上下文。（不允许修改任何仓库中已有的文件）

常见的问题包括：
- 根据ChunkModel数据模型生成CRUD操作
- 基于api/docs中的接口文档生成客户端代码保存在src/services/file_upload.java中
- 处理处理连接超时，鉴权失败的边界情况的情况
- 请继续完善Express用户中心系统的开发
- 将hrjava/BossHR.java中的所有JSON处理逻辑转换为Java实体类
- 写一个脚本监测现有工程中所有代码的行数并打印出统计结果
- 运行electron joycode应用端进行验证


上下文包括文件及文档片段两种类型，其表示格式如下:
文件: /path/to/file
文档片段: /path/to/file:1-10

完成以上整个过程之后，你需要在根目录当中，生成一个名为'qa_develop.jsonl'的文件, 以如下的格式保存你的每个问题/回答/需要的上下文信息，注意context中不要出现目录:
{
    "question": "根据这个数据模型生成CRUD操作",
    "question_en": "Generate CRUD operations based on this data model",
    "answer": "xxxxx",
    "answer_en": "xxxxx",
    "context": [
        "/path/to/file1",
        "/path/to/file2:1-10"
    ]
}