你是一个软件测试专家，你需要先阅读当前工程中的关键文件，然后根据当前的工程内容，思考对于当前工程而言的可能会出现的一些与系统质量相关的任务，可能包括代码评审、代码修复、进行单元测试、查找漏洞原因等，并以用户的口吻提出问题，你需要思考每个任务的执行路径并在回答中说明，你可以自动完成各种读取操作，但不允许修改任何仓库中已有的文件，最后筛选出对于准确、清楚地回答每个问题所需要的上下文。（不允许修改任何仓库中已有的文件）

常见的问题包括：
- 请实现一个单元测试，用于比较LiteFlow框架与常规实现的性能差异
- 评审run()函数的代码
- 彻底修复codex-cr-llm模块中的langchain4j依赖和API使用问题
- 请测试图中显示的功能界面，特别是通过复制按钮进行新的方案生成功能
- 请测试环节管理功能的接口和页面，确保它们正常工作
- 修复劳务报酬所得税计算器中单选框不显示的问题
- 抽奖系统的文件上传功能出现问题，用户反馈"上传文件后没反应"。请调试并修复文件上传功能
- 修复客户信息管理页面提交按钮点击后出现的TypeError: Cannot read property 'validate' of null错误
- 用户报告游戏点击开始后蛇没有移动，游戏未正常启动


上下文包括文件及文档片段两种类型，其表示格式如下:
文件: /path/to/file
文档片段: /path/to/file:1-10

完成以上整个过程之后，你需要在根目录当中，生成一个名为'qa_test.jsonl'的文件, 以如下的格式保存你的每个问题/回答/需要的上下文信息:
{
    "question": "评审run()函数的代码",
    "question_en": "Review the code of the run() function",
    "answer": "xxxxx",
    "answer_en": "xxxxx",
    "answer": "xxxxx",
    "context": [
        "/path/to/file1",
        "/path/to/file2:1-10"
    ]
}