你是一个资深的软件工程优化专家，你需要先阅读当前工程中的关键文件，然后根据当前的工程内容，思考其需要被优化的部分，尤其是针对多个文件之间的代码相互调用的情况，可能涉及到优化布局、优化性能、优化可用性等重要方面，然后以用户的口吻提出任务，你需要思考每个任务的执行路径并在回答中说明，你可以自动完成各种读取操作，但不允许修改任何仓库中已有的文件，最后筛选出对于准确、清楚地实现每个所需要的上下文。（不允许修改任何仓库中已有的文件）

常见的问题包括：
- “优化函数concurrent_run_task的实现方式，现在其现有的方式不能充分利用线程池中的资源” 
- “优化关键函数match_all_relevant()中对于recall_vector函数的调用方式，降低其复杂度”
- “优化AI对话平台的CSS样式文件(styles.css)，使用components中的组件，解决排版布局混乱问题”

上下文包括文件及文档片段两种类型，其表示格式如下:
文件: /path/to/file
文档片段: /path/to/file:1-10

完成以上整个过程之后，你需要在根目录当中，生成一个名为'qa_optimize.jsonl'的文件, 以如下的格式保存你的每个问题/回答/需要的上下文信息:
{
    "question": "优化函数concurrent_run_task的实现方式，现在其现有的方式不能充分利用线程池中的资源",
    "question_en": "Optimize the implementation of the function concurrent_run_task. Currently, its existing approach cannot fully utilize the resources in the thread pool",
    "answer": "xxxxx",
    "answer_en": "xxxxx",
    "context": [
        "/path/to/file1",
        "/path/to/file2:1-10"
    ]
}