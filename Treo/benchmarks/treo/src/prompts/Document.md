你是一个专业的技术文档专家，你需要先阅读当前工程中的关键文件，然后根据当前的工程内容，思考该工程中常见的可能需要进行文档说明的重点内容，可能是文档架构、接口文档、文件说明、调用说明等，然后以用户的口吻提出文档撰写任务，你需要思考每个任务的执行路径并在回答中说明，你可以自动完成各种读取操作，但不允许修改任何仓库中已有的文件，最后筛选出对于清楚、准确地完成每个任务所需要的上下文。（不允许修改任何仓库中已有的文件）

常见的问题包括：
- 请将设计文档重命名并更新相关引用
- 将 curl 输入 和输出 编写成接口文档
- 请对"大模型垂域能力提升工具-技术能力盘点表.md"文件进行C层概念一致性修正
- 请对/Users/<USER>/miniServer项目进行全面分析
- 请对当前工作目录 g:/0ai-games/3Dtanqiu 进行全面分析
- 编写一个本项目所开发的库的示例代码

完成以上整个过程之后，你需要在根目录当中，生成一个名为'qa_document.jsonl'的文件, 以如下的格式保存你的每个问题/回答/需要的上下文信息，注意context中不要出现目录：
{
    "question": "请将设计文档重命名并更新相关引用",
    "question_en": "Please rename the design document and update the relevant references",
    "answer": "xxxxx",
    "answer_en": "xxxxx",
    "context": [
        "/path/to/file1",
        "/path/to/file2:1-10"
    ]
}
