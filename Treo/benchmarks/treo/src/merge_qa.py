#!/usr/bin/env python3
"""
合并仓库中的QA文件

输入：仓库目录地址repos_dir（default=/benchmarks/treo/data/repos）
输出：qas.json的存储路径，其中合并了每个仓库的所有qa_*.jsonl文件（default=/benchmarks/treo/data/repos/qas.json）

qas.json的格式为：
[
 {
     "repo": "repo_url",
     "commit": "commit_hash",
     "qa": [
         {
             "question": "xxxxx",
             "answer": "xxxxx",
             "role": "architect",
             "context": [
                 "/path/to/file1",
                 "/path/to/file2:1-10"
             ]
         },
         ...
     ]
 }
]

主要逻辑：
1. 扫描repos_dir中的所有仓库目录，识别方法为仓库的目录中包含.git或者.github目录
2. 通过git命令获取当前仓库的remote_url以及当前所在的commit hash，如果获取不到则打印出对应的日志信息
3. 对于每个仓库目录，加载其中的所有qa_*.jsonl文件，并且解析其内容
4. 若仓库目录中缺少个别或所有qa_*.jsonl文件，输出对应的日志
5. 检查qa_*.jsonl中每个qa中对应context的路径是否正确，其路径是否属于当前仓库的路径下，若不属于则输出对应的日志
"""

import json
import subprocess
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def is_repository(directory: Path) -> bool:
    """检查目录是否为Git仓库"""
    return (directory / '.git').exists() or (directory / '.github').exists()


def get_git_info(repo_path: Path) -> Tuple[Optional[str], Optional[str]]:
    """获取Git仓库的remote URL和commit hash"""
    try:
        # 获取remote URL
        result = subprocess.run(
            ['git', 'remote', 'get-url', 'origin'],
            cwd=repo_path,
            capture_output=True,
            text=True,
            check=True
        )
        remote_url = result.stdout.strip()

        # 获取当前commit hash
        result = subprocess.run(
            ['git', 'rev-parse', 'HEAD'],
            cwd=repo_path,
            capture_output=True,
            text=True,
            check=True
        )
        commit_hash = result.stdout.strip()

        return remote_url, commit_hash

    except subprocess.CalledProcessError as e:
        logger.warning(f"无法获取仓库 {repo_path} 的Git信息: {e}")
        return None, None
    except Exception as e:
        logger.error(f"获取仓库 {repo_path} 的Git信息时发生错误: {e}")
        return None, None


def find_qa_files(repo_path: Path) -> List[Path]:
    """查找仓库中的所有qa_*.jsonl文件"""
    qa_files = list(repo_path.glob('qa_*.jsonl'))
    return qa_files


def load_qa_file(qa_file: Path) -> List[Dict[str, Any]]:
    """加载单个qa_*.jsonl文件"""
    qa_data = []
    try:
        with open(qa_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()

            # 尝试解析为单个JSON对象（多行格式）
            try:
                qa_item = json.loads(content)
                if isinstance(qa_item, dict):
                    qa_data.append(qa_item)
                elif isinstance(qa_item, list):
                    qa_data.extend(qa_item)
                return qa_data
            except json.JSONDecodeError:
                pass

            # 尝试解析多个JSON对象（每个对象可能跨多行，对象间用换行分隔）
            try:
                qa_data = parse_multi_json_objects(content)
                if qa_data:
                    return qa_data
            except Exception:
                pass

            # 尝试按行解析JSONL格式
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    qa_item = json.loads(line)
                    qa_data.append(qa_item)
                except json.JSONDecodeError as e:
                    logger.warning(f"文件 {qa_file} 第 {line_num} 行JSON解析错误: {e}")

    except Exception as e:
        logger.error(f"读取文件 {qa_file} 时发生错误: {e}")

    return qa_data


def parse_multi_json_objects(content: str) -> List[Dict[str, Any]]:
    """解析包含多个JSON对象的内容，每个对象可能跨多行"""
    qa_data = []

    # 使用简单的括号计数来分割JSON对象
    objects = []
    current_object = ""
    brace_count = 0
    in_string = False
    escape_next = False

    for char in content:
        if escape_next:
            escape_next = False
            current_object += char
            continue

        if char == '\\':
            escape_next = True
            current_object += char
            continue

        if char == '"' and not escape_next:
            in_string = not in_string

        if not in_string:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1

        current_object += char

        # 当括号计数为0且当前对象不为空时，说明一个完整的JSON对象结束
        if brace_count == 0 and current_object.strip():
            objects.append(current_object.strip())
            current_object = ""

    # 解析每个JSON对象
    for i, obj_str in enumerate(objects):
        if not obj_str:
            continue
        try:
            qa_item = json.loads(obj_str)
            if isinstance(qa_item, dict):
                qa_data.append(qa_item)
        except json.JSONDecodeError as e:
            logger.warning(f"解析第 {i+1} 个JSON对象时出错: {e}")

    return qa_data


def validate_context_paths(qa_data: List[Dict[str, Any]], repo_path: Path) -> None:
    """验证context中的路径是否属于当前仓库"""
    repo_abs_path = repo_path.resolve()

    for qa_item in qa_data:
        # 跳过非字典类型的项
        if not isinstance(qa_item, dict):
            continue

        if 'context' not in qa_item:
            continue

        for context_path in qa_item.get('context', []):
            # 提取文件路径（去除行号信息）
            file_path = context_path.split(':')[0]

            # 处理路径
            if file_path.startswith('/'):
                # 以/开头的路径，尝试作为相对于仓库根目录的路径
                # 去掉开头的/
                relative_path = file_path.lstrip('/')
                abs_context_path = (repo_path / relative_path).resolve()
            else:
                # 相对路径，相对于仓库根目录
                abs_context_path = (repo_path / file_path).resolve()

            # 检查路径是否在仓库目录下
            try:
                abs_context_path.relative_to(repo_abs_path)
                # 检查文件是否实际存在（可选，因为可能有些文件在生成QA时存在但现在不存在）
                # if not abs_context_path.exists():
                #     logger.warning(
                #         f"仓库 {repo_path} 中的context路径 '{context_path}' 指向的文件不存在"
                #     )
            except ValueError:
                logger.warning(
                    f"仓库 {repo_path} 中的context路径 '{context_path}' 不属于当前仓库"
                )


def process_repository(repo_path: Path) -> Optional[Dict[str, Any]]:
    """处理单个仓库"""
    logger.info(f"处理仓库: {repo_path}")

    # 获取Git信息
    remote_url, commit_hash = get_git_info(repo_path)
    if not remote_url or not commit_hash:
        logger.warning(f"跳过仓库 {repo_path}，无法获取Git信息")

    # 查找qa文件
    qa_files = find_qa_files(repo_path)
    if len(qa_files) != 4:
        logger.warning(f"仓库 {repo_path} 中没有找到4个qa_*.jsonl文件，只有{len(qa_files)}个")

    # 加载所有qa数据
    all_qa_data = []
    for qa_file in qa_files:
        logger.info(f"加载文件: {qa_file}")
        qa_data = load_qa_file(qa_file)

        if not qa_data:
            logger.warning(f"文件 {qa_file} 为空或无法解析")
            continue

        # 添加role信息（从文件名提取）
        role = qa_file.stem.replace('qa_', '')  # 去除'qa_'前缀
        for qa_item in qa_data:
            if isinstance(qa_item, dict):
                qa_item['role'] = role
            else:
                logger.warning(f"文件 {qa_file} 中的QA项不是字典格式: {type(qa_item)}")

        # 验证context路径
        validate_context_paths(qa_data, repo_path)

        all_qa_data.extend(qa_data)

    if not all_qa_data:
        logger.warning(f"仓库 {repo_path} 中没有有效的QA数据")
        return None

    return {
        'repo': remote_url,
        'commit': commit_hash,
        "local_path": repo_path.as_posix(),
        'qa': all_qa_data
    }


def scan_repositories(repos_dir: Path) -> List[Path]:
    """扫描repos_dir中的所有仓库目录"""
    repositories = []

    def scan_directory(directory: Path, max_depth: int = 5, current_depth: int = 0):
        """递归扫描目录"""
        if current_depth > max_depth:
            return

        if not directory.is_dir():
            return

        # 检查当前目录是否为仓库
        if is_repository(directory):
            repositories.append(directory)
            return  # 找到仓库后不再深入扫描

        # 继续扫描子目录
        try:
            for item in directory.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    scan_directory(item, max_depth, current_depth + 1)
        except PermissionError:
            logger.warning(f"无权限访问目录: {directory}")

    scan_directory(repos_dir)
    return repositories

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='合并仓库中的QA文件')
    parser.add_argument(
        '--repos-dir',
        type=str,
        default='./benchmarks/treo/data/repos',
        help='仓库目录路径 (默认: /benchmarks/treo/data/repos)'
    )
    parser.add_argument(
        '--output',
        type=str,
        default='./benchmarks/treo/data/qas.json',
        help='输出文件路径 (默认: repos_dir/qas.json)'
    )

    args = parser.parse_args()

    # 处理路径
    repos_dir = Path(args.repos_dir)
    if not repos_dir.exists():
        logger.error(f"仓库目录不存在: {repos_dir}")
        return 1

    # 设置输出路径
    if args.output:
        output_path = Path(args.output)
    else:
        output_path = repos_dir / 'qas.json'

    logger.info(f"扫描仓库目录: {repos_dir}")
    logger.info(f"输出文件: {output_path}")

    # 扫描仓库
    repositories = scan_repositories(repos_dir)
    logger.info(f"找到 {len(repositories)} 个仓库")

    if not repositories:
        logger.warning("没有找到任何仓库")
        return 1

    # 处理所有仓库
    all_qa_data = []
    for repo_path in repositories:
        repo_data = process_repository(repo_path)
        if repo_data:
            all_qa_data.append(repo_data)

    logger.info(f"成功处理 {len(all_qa_data)} 个仓库")

    # 保存结果
    try:
        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(all_qa_data, f, ensure_ascii=False, indent=2)
        logger.info(f"结果已保存到: {output_path}")
    except Exception as e:
        logger.error(f"保存结果时发生错误: {e}")
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
