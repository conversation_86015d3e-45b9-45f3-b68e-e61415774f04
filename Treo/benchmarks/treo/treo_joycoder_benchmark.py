from typing import Dict, List
import json
import os
import hashlib
from collections import defaultdict
import time
from benchmarks.benchmark_model import BenchmarkModel, DataSampleTerm
from benchmarks.treo.treo_utils import TreoUtils
from api.codebase import CodeBaseApi
from api.schema import FileUpload, MerkleTreeNode
from utils.logger import logger
from utils.file import DEFAULT_IGNORES, READABLE_FILE_TYPES


class TreoJoycoderBenchmarkModel(BenchmarkModel):
    def __init__(self, base_service: CodeBaseApi, qas_file: str, output_dir: str, ):
        super().__init__(base_service, output_dir)
        self.base_service = base_service
        
        # 加载QA数据
        with open(qas_file, 'r', encoding='utf-8') as f:
            self.qas_data = json.load(f)

        # 存储项目ID映射
        self.project_ids_map = {}

    def init_dataset(self):
        """初始化数据集，为每个项目创建项目并上传文件"""
        logger.info("⏱️ Starting dataset initialization...")
        for repo_data in self.qas_data:
            repo_local_path = repo_data['local_path']
            repo_url = repo_data['repo']

            logger.info(f"Processing repository: {repo_url}")

            # 创建项目
            try:
                create_response = self.base_service.create_project(
                    repo_url=repo_url,
                    username="treo_benchmark",
                    branch="main",
                    source="local"
                )

                if create_response.is_new:
                    logger.info(f"Created new project: {create_response.project_id}")
                else:
                    logger.info(f"Using existing project: {create_response.project_id}")

                # 存储项目ID映射
                self.project_ids_map[repo_local_path] = create_response.project_id

                if not create_response.is_new:
                    continue

                # 如果是新项目，需要上传文件
                self._upload_repository_files(repo_local_path, create_response.project_id)

            except Exception as e:
                logger.error(f"Failed to create project for {repo_url}: {e}")
                continue

        logger.info("✅ Dataset initialization completed")

    def _upload_repository_files(self, repo_local_path: str, project_id: str):
        """上传仓库文件到项目"""
        logger.info(f"Uploading files for repository: {repo_local_path}")

        # 读取仓库目录中的所有文件
        files_to_upload = []

        if not os.path.exists(repo_local_path):
            logger.warning(f"Repository path does not exist: {repo_local_path}")
            return

        # 遍历目录，收集所有代码文件
        for root, dirs, files in os.walk(repo_local_path):
            # 跳过隐藏目录和常见的忽略目录
            dirs[:] = [d for d in dirs if not d in DEFAULT_IGNORES]
            
            if root != repo_local_path:
                files_to_upload.append(FileUpload(
                        file_path=root.removeprefix(repo_local_path).removeprefix(os.path.sep),
                        file_content='DIRECTORY_TAG',
                        file_hash=hashlib.sha256(repo_local_path.encode('utf-8')).hexdigest()
                    ))
            
            for file in files:
                # 跳过非可读文件
                if not file.endswith(tuple(READABLE_FILE_TYPES)) or file in DEFAULT_IGNORES:
                    continue

                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, repo_local_path)

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if len(content) < 1:
                            continue

                    files_to_upload.append(FileUpload(
                        file_path=relative_path,
                        file_content=content,
                        file_hash=hashlib.sha256(content.encode('utf-8')).hexdigest()
                    ))
                except (UnicodeDecodeError, IOError) as e:
                    logger.warning(f"Failed to read file {file_path}: {e}")
                    continue
            
        if not files_to_upload:
            logger.warning(f"No files found to upload for {repo_local_path}")
            return

        # 创建Merkle树
        merkle_tree = self._create_merkle_tree(repo_local_path, files_to_upload)

        # 上传文件
        try:
            self.base_service.load_context_files(project_id, merkle_tree, [file for file in files_to_upload if file.file_path.endswith(tuple(READABLE_FILE_TYPES))])
            logger.info(f"Successfully uploaded {len(files_to_upload)} files for project {project_id}")
        except Exception as e:
            logger.error(f"Failed to upload files for project {project_id}: {e}")

    def _create_merkle_tree(self, repo_path: str, files: List[FileUpload]) -> MerkleTreeNode:
        """创建简化的Merkle树"""
        # 为简化起见，创建一个基本的Merkle树结构
        # 创建一个具有完整父子关系的Merkle树结构，由files的file_path判断父子关系
        # 可以先对files根据file_path的长度进行排序，然后顺序处理
        files.sort(key=lambda x: len(x.file_path))
        file_path2node = []
        top_nodes = []
        for file in files:
            cur_node = None

            is_child_flag = False
            for it_path, it_node in file_path2node[::-1]:
                if file.file_path.startswith(it_path) and file.file_path[len(it_path)] == os.path.sep:
                    cur_node = MerkleTreeNode(
                            path=file.file_path.removeprefix(it_path).removeprefix(os.path.sep),
                            is_file=file.file_path.endswith(tuple(READABLE_FILE_TYPES)),
                            hash=hashlib.sha256(file.file_content.encode('utf-8')).hexdigest()
                        )
                    it_node.children.append(cur_node)
                    is_child_flag = True
                    break

            if not is_child_flag:
                cur_node = MerkleTreeNode(
                            path=file.file_path.removeprefix(os.path.sep),
                            is_file=file.file_path.endswith(tuple(READABLE_FILE_TYPES)),
                            hash=hashlib.sha256(file.file_content.encode('utf-8')).hexdigest()
                        )
                top_nodes.append(cur_node)
                
            file_path2node.append((file.file_path, cur_node))
        
        # 创建根节点
        root_hash = hashlib.sha256(''.join(node.hash for node in top_nodes).encode('utf-8')).hexdigest()
        return MerkleTreeNode(
            path=".",
            is_file=False,
            hash=root_hash,
            children=top_nodes
        )

    def evaluate(self, top_k: int = 30, k_values: List[int] = [5, 10, 30], question_key: str = "question", *args, **kwargs) -> Dict[str, float]:
        """评估Joycoder在TREO数据集上的性能"""
        logger.info(f"🔄 Starting evaluation with k_values={k_values}")

        # 转换数据为样本格式
        samples = self._convert_to_samples(question_key)

        all_metrics = defaultdict(list)
        total_queries = len(samples)

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 按项目分组处理
        project_samples = defaultdict(list)
        for sample in samples:
            project_samples[sample.project_id].append(sample)

        for project_id, project_sample_list in project_samples.items():
            logger.info(f"Evaluating project: {project_id} ({len(project_sample_list)} queries)")

            project_metrics = defaultdict(list)

            for sample in project_sample_list:
                question = sample.query
                relevant_docs = sample.context

                # 获取对应的repo信息
                repo_url, repo_local_path = self._get_repo_info_by_project_id(project_id)

                # 执行检索，使用最大的k值来获取足够的结果
                max_k = max(k_values) if k_values else top_k
                try:
                    retrieved_results = self.base_service.retrieve(
                        project_id=project_id,
                        repo_url=repo_url,
                        branch="main",
                        username="treo_benchmark",
                        query=question,
                        retrieval_type="chunk",
                        max_context_chunks=max_k
                    )
                    # 按score排序并返回结果
                    retrieved_docs = [self._parse_context_path(item[0]) for item in sorted(retrieved_results.items(), key=lambda x: x[1], reverse=True)]

                    # 计算包含MAP的指标
                    metrics = TreoUtils.calculate_metrics_with_map(retrieved_docs, relevant_docs, k_values)

                    # 累积指标
                    for metric_name, value in metrics.items():
                        project_metrics[metric_name].append(value)
                        all_metrics[metric_name].append(value)

                except Exception as e:
                    logger.error(f"Failed to retrieve for query '{question[:50]}...': {e}")
                    # 添加零分指标
                    zero_metrics = {}
                    for k in k_values:
                        zero_metrics.update({
                            f'precision_at_{k}': 0.0,
                            f'recall_at_{k}': 0.0,
                            f'f1_at_{k}': 0.0,
                            f'map_at_{k}': 0.0,
                            f'ndcg_at_{k}': 0.0,
                        })
                    for metric_name, value in zero_metrics.items():
                        project_metrics[metric_name].append(value)
                        all_metrics[metric_name].append(value)

            # 计算项目级别的平均指标
            project_avg_metrics = {}
            for metric_name, values in project_metrics.items():
                project_avg_metrics[metric_name] = sum(values) / len(values) if values else 0.0

            logger.info(f"Project {project_id} metrics: {project_avg_metrics}")

        # 计算总体平均指标
        avg_metrics = {}
        for metric_name, values in all_metrics.items():
            avg_metrics[metric_name] = sum(values) / len(values) if values else 0.0

        logger.info(f"📊 Overall evaluation results: {avg_metrics}")
        logger.info(f"Total queries processed: {total_queries}")

        # 保存结果到文件
        k_values_str = "_".join(map(str, k_values))

        # 文件保存到日期中 
        output_file = os.path.join(self.output_dir, f"treo_joycoder_results_{question_key}_k{k_values_str}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metrics': avg_metrics,
                'total_queries': total_queries,
                'k_values': k_values,
                'max_k': max(k_values) if k_values else top_k
            }, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Results saved to: {output_file}")

        return avg_metrics

    def _convert_to_samples(self, question_key: str = "question") -> List[DataSampleTerm]:
        """将TREO数据转换为DataSampleTerm格式"""
        samples = []

        for repo_data in self.qas_data:
            repo_local_path = repo_data['local_path']
            project_id = self.project_ids_map[repo_local_path]
            qa_list = repo_data['qa']

            for qa_item in qa_list:
                question = qa_item[question_key]
                answer = qa_item.get('answer', '')
                context_paths = qa_item['context']

                # 解析相关文档路径
                context = []
                for context_path in context_paths:
                    file_path = self._parse_context_path(context_path)
                    context.append(file_path)

                sample = DataSampleTerm(
                    project_id=project_id,
                    query=question,
                    answer=answer,
                    context=context
                )
                samples.append(sample)

        return samples

    def _parse_context_path(self, context_path: str) -> str:
        """解析context路径，提取文件路径"""
        # context格式: "/path/to/file.py:start_line-end_line"
        if ':' in context_path:
            context_path = context_path.split(':')[0]
        
        if context_path.startswith('./'):
            return context_path[2:]
        elif context_path.startswith('/'):
            return context_path[1:]
        
        return context_path

    def _get_repo_info_by_project_id(self, project_id: str) -> tuple:
        """根据项目ID获取仓库信息"""
        for repo_data in self.qas_data:
            repo_local_path = repo_data['local_path']
            if self.project_ids_map.get(repo_local_path) == project_id:
                return repo_data['repo'], repo_local_path
        raise ValueError(f"No repository found for project_id: {project_id}")