from typing import Dict, List
import json
import os
from collections import defaultdict

from benchmarks.benchmark_model import BenchmarkModel
from services.lexcial.lexcial_tfidf import LexicalTFIDF
from utils.logger import logger
from benchmarks.treo.treo_utils import TreoUtils

DEFAULT_CACHE_DIR = './cache/tfidf_treo'

class TreoTFIDFLexicalBenchmarkModel(BenchmarkModel):
    def __init__(self, base_service: LexicalTFIDF, qas_file: str, output_dir: str, cache_dir: str = DEFAULT_CACHE_DIR):
        super().__init__(base_service, output_dir)
        self.base_service = base_service
        self.cache_dir = cache_dir

        # 加载QA数据
        with open(qas_file, 'r', encoding='utf-8') as f:
            self.qas_data = json.load(f)

        # 存储项目ID映射
        self.project_ids = {}
        
        logger.info(f"Loaded {len(self.qas_data)} repositories with QA data")

    def init_dataset(self):
        """初始化数据集，为每个项目创建TFIDF索引"""
        logger.info("⏱️ Starting dataset initialization...")

        for repo_data in self.qas_data:            
            local_path = repo_data.get('local_path')

            # 跳过无效数据
            if not local_path:
                logger.warning(f"Skipping invalid repo data: local_path={local_path}")
                continue

            # 创建项目ID
            project_id = TreoUtils.get_project_id(local_path)
            self.project_ids[local_path] = project_id

            logger.info(f"Local path: {local_path}")
            logger.info(f"Project ID: {project_id}")

            # 检查本地路径是否存在
            if not os.path.exists(local_path):
                logger.warning(f"Local path does not exist: {local_path}, skipping...")
                continue

            # 加载项目文件并构建索引
            # LexicalTFIDF的load_context_files方法参数是(project_dir, project_id)
            # project_dir是源代码目录，project_id用于生成缓存文件名
            result = self.base_service.load_context_files(local_path, self.cache_dir, project_id)
            logger.info(f"Index result: {result}")

        logger.info("✅ Dataset initialization completed")

    def _parse_context_path(self, context_path: str) -> str:
        """解析context路径，提取文件路径"""
        # context格式: "/path/to/file.py:start_line-end_line"
        if ':' in context_path:
            return context_path.split(':')[0]
        return context_path

    def evaluate(self, top_k: int = 10, k_values: List[int] = [1, 3, 5, 10], *args, **kwargs) -> Dict[str, float]:
        """评估TFIDF在TREO数据集上的性能"""
        logger.info(f"🔄 Starting evaluation with k_values={k_values}")

        samples = TreoUtils.convert_to_samples(self.qas_data, self.project_ids)

        all_metrics = defaultdict(list)
        total_queries = len(samples)

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        project_id2project_dir = {}
        for local_path, project_id in self.project_ids.items():
            project_id2project_dir[project_id] = local_path
            
        # 按项目分组处理
        project_samples = defaultdict(list)
        for sample in samples:
            project_samples[sample.project_id].append(sample)

        for project_id, project_sample_list in project_samples.items():
            logger.info(f"Evaluating project: {project_id} ({len(project_sample_list)} queries)")
            
            self.base_service.load_context_files(project_id2project_dir[project_id], self.cache_dir, project_id)

            project_metrics = defaultdict(list)

            for sample in project_sample_list:
                question = sample.query
                relevant_docs = sample.context

                # 执行检索，使用最大的k值来获取足够的结果
                max_k = max(k_values) if k_values else top_k
                retrieved_results = self.base_service.retrieve(project_id, question, max_k)
                # 按分数排序并返回结果
                retrieved_docs = [self._parse_context_path(file_path) for file_path, score in retrieved_results.items()]

                # 计算包含MAP的指标
                metrics = TreoUtils.calculate_metrics_with_map(retrieved_docs, relevant_docs, k_values)

                # 累积指标
                for metric_name, value in metrics.items():
                    project_metrics[metric_name].append(value)
                    all_metrics[metric_name].append(value)

            # 计算该项目的平均指标
            project_avg_metrics = {}
            for metric_name, values in project_metrics.items():
                project_avg_metrics[metric_name] = sum(values) / len(values) if values else 0.0

            logger.info(f"Project {project_id} metrics: {project_avg_metrics}")

        # 计算总体平均指标
        final_metrics = {}
        for metric_name, values in all_metrics.items():
            final_metrics[metric_name] = sum(values) / len(values) if values else 0.0

        logger.info(f"📊 Final evaluation results (total queries: {total_queries}):")
        for metric_name, value in final_metrics.items():
            logger.info(f"  {metric_name}: {value:.4f}")

        # 保存结果到文件
        k_values_str = "_".join(map(str, k_values))
        output_file = os.path.join(self.output_dir, f"treo_tfidf_results_k{k_values_str}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metrics': final_metrics,
                'total_queries': total_queries,
                'k_values': k_values,
                'max_k': max(k_values) if k_values else top_k
            }, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Results saved to: {output_file}")

        return final_metrics
