from typing import Dict, List
import json
import os
import hashlib
import uuid
from collections import defaultdict
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from benchmarks.benchmark_model import BenchmarkModel, DataSampleTerm
from benchmarks.treo.treo_utils import TreoUtils
from api.codebase_dev import CodeBaseDevApi
from utils.logger import logger

class TreoCodebaseBenchmarkModel(BenchmarkModel):
    def __init__(self, base_service: CodeBaseDevApi, qas_file: str, output_dir: str, ):
        super().__init__(base_service, output_dir)
        self.base_service = base_service
        
        # 加载QA数据
        with open(qas_file, 'r', encoding='utf-8') as f:
            self.qas_data = json.load(f)

    def init_dataset(self):
        pass

        logger.info("✅ Dataset initialization completed")

    def evaluate(self, top_k: int = 30, k_values: List[int] = [5, 10, 30], question_key: str = "question", max_workers: int = 4, search_tool: str = "term_sparse") -> Dict[str, float]:
        """评估Joycoder在TREO数据集上的性能
        
        Args:
            top_k: 最大检索数量
            k_values: 评估的k值列表
            question_key: 问题字段名
            max_workers: 并发线程数，默认为4
            *args, **kwargs: 其他参数
        """
        logger.info(f"🔄 Starting evaluation with k_values={k_values}, max_workers={max_workers}")

        # 转换数据为样本格式
        samples = self._convert_to_samples(question_key)

        all_metrics = defaultdict(list)
        total_queries = len(samples)

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 按项目分组处理
        project_samples = defaultdict(list)
        for sample in samples:
            project_samples[sample.project_id].append(sample)

        def process_single_query(sample, project_id, trace_id):
            """处理单个查询的辅助函数"""
            question = sample.query
            relevant_docs = sample.context
            
            # 执行检索，使用最大的k值来获取足够的结果
            max_k = max(k_values) if k_values else top_k
            try:
                retrieved_chunk_results = self.base_service.retrieve(
                    workspaceName=project_id,
                    query=question,
                    search_tool=search_tool,
                    trace_id=trace_id
                )

                retrieved_file_results = defaultdict(float)
                for chunk_path, score in retrieved_chunk_results.items():
                    file_path = self._parse_context_path(chunk_path)
                    retrieved_file_results[file_path] += score

                # 按score排序并返回结果
                retrieved_docs = [self._parse_context_path(item[0]) for item in sorted(retrieved_file_results.items(), key=lambda x: x[1], reverse=True)]

                # 计算包含MAP的指标
                
                metrics = TreoUtils.calculate_metrics_with_map(retrieved_docs, relevant_docs, k_values)
                logger.info(f"Trace: {trace_id}, project: {project_id}, question: {question}")
                logger.info(f"Trace: {trace_id}, project: {project_id}, metrics: {json.dumps(metrics, ensure_ascii=False, indent=2)}")
                logger.info(f"Trace: {trace_id}, project: {project_id}, retrieved_docs: {json.dumps(retrieved_docs, ensure_ascii=False, indent=2)}")
                logger.info(f"Trace: {trace_id}, project: {project_id}, relevant_docs: {json.dumps(relevant_docs, ensure_ascii=False, indent=2)}")
                logger.info("==========================================================")
                return metrics
                
            except Exception as e:
                logger.error(f"Failed to retrieve for query '{question[:50]}...':", e)
                # 添加零分指标
                zero_metrics = {}
                for k in k_values:
                    zero_metrics.update({
                        f'precision_at_{k}': 0.0,
                        f'recall_at_{k}': 0.0,
                        f'f1_at_{k}': 0.0,
                        f'map_at_{k}': 0.0,
                        f'ndcg_at_{k}': 0.0,
                    })
                return zero_metrics

        for project_id, project_sample_list in project_samples.items():
            logger.info(f"Evaluating project: {project_id} ({len(project_sample_list)} queries)")

            project_metrics = defaultdict(list)

            # 使用线程池并发处理查询
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有查询任务
                future_to_sample = {
                    executor.submit(process_single_query, sample, project_id, uuid.uuid4().hex): sample
                    for sample in project_sample_list
                }
                
                # 收集结果
                for future in as_completed(future_to_sample):
                    try:
                        metrics = future.result()
                        # 累积指标
                        for metric_name, value in metrics.items():
                            project_metrics[metric_name].append(value)
                            all_metrics[metric_name].append(value)
                    except Exception as e:
                        logger.error(f"Error processing query: ", e)
                        # 添加零分指标
                        zero_metrics = {}
                        for k in k_values:
                            zero_metrics.update({
                                f'precision_at_{k}': 0.0,
                                f'recall_at_{k}': 0.0,
                                f'f1_at_{k}': 0.0,
                                f'map_at_{k}': 0.0,
                                f'ndcg_at_{k}': 0.0,
                            })
                        for metric_name, value in zero_metrics.items():
                            project_metrics[metric_name].append(value)
                            all_metrics[metric_name].append(value)

            # 计算项目级别的平均指标
            project_avg_metrics = {}
            for metric_name, values in project_metrics.items():
                project_avg_metrics[metric_name] = sum(values) / len(values) if values else 0.0

            logger.info(f"Project {project_id} metrics: {project_avg_metrics}")

        # 计算总体平均指标
        avg_metrics = {}
        for metric_name, values in all_metrics.items():
            avg_metrics[metric_name] = sum(values) / len(values) if values else 0.0

        logger.info(f"📊 Overall evaluation results: {avg_metrics}")
        logger.info(f"Total queries processed: {total_queries}")

        # 保存结果到文件
        k_values_str = "_".join(map(str, k_values))

        # 文件保存到日期中 
        output_file = os.path.join(self.output_dir, f"treo_joycoder_results_{question_key}_k{k_values_str}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metrics': avg_metrics,
                'total_queries': total_queries,
                'k_values': k_values,
                'max_k': max(k_values) if k_values else top_k
            }, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Results saved to: {output_file}")

        return avg_metrics

    def _convert_to_samples(self, question_key: str = "question") -> List[DataSampleTerm]:
        """将TREO数据转换为DataSampleTerm格式"""
        samples = []

        for repo_data in self.qas_data:
            repo_local_path = repo_data['local_path']
            project_id = repo_local_path.split('/')[-1]
            qa_list = repo_data['qa']

            for qa_item in qa_list:
                question = qa_item[question_key]
                answer = qa_item.get('answer', '')
                context_paths = qa_item['context']

                # 解析相关文档路径
                context = []
                for context_path in context_paths:
                    file_path = self._parse_context_path(context_path)
                    context.append(file_path)

                sample = DataSampleTerm(
                    project_id=project_id,
                    query=question,
                    answer=answer,
                    context=context
                )
                samples.append(sample)

        return samples

    def _parse_context_path(self, context_path: str) -> str:
        """解析context路径，提取文件路径"""
        # context格式: "/path/to/file.py:start_line-end_line"
        if ':' in context_path:
            context_path = context_path.split(':')[0]
        
        if context_path.startswith('./'):
            return context_path[2:]
        elif context_path.startswith('/'):
            return context_path[1:]
        
        return context_path