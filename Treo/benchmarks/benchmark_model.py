from abc import ABC, abstractmethod
from typing import Dict, List
from services.service import IService
from pydantic import BaseModel, Field
import time
import os
from utils.logger import logger


class DataSampleTerm(BaseModel):
    project_id: str = Field(..., description="项目ID")
    query: str = Field(..., description="查询问题")
    answer: str = Field(..., description="查询答案")
    context: List[str] = Field(..., description="上下文信息")


class BenchmarkModel(ABC):
    def __init__(self, base_service: IService, output_dir: str, **kwargs):
        self.output_dir = output_dir
        if not os.path.exists(self.output_dir):            
            os.makedirs(self.output_dir, exist_ok=True)
            logger.info(f"Created output directory: {self.output_dir}")
            
    @abstractmethod
    def init_dataset(self):
        pass

    @abstractmethod
    def evaluate(self, samples: List[DataSampleTerm], top_k: int, *args, **kwargs) -> Dict[str, float]:
        pass
