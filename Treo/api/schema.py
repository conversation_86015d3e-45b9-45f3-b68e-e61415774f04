"""
API请求和响应的Pydantic模型定义
基于API文档定义所有的数据结构
"""

from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field
from uuid import uuid4

# ============================================================================
# 项目管理相关模型
# ============================================================================

class CreateProjectRequest(BaseModel):
    """创建项目请求"""
    repo_url: str = Field(..., description="代码仓库的URL")
    username: str = Field(..., description="用户标识符")
    branch: str = Field(..., description="分支名称")
    source: str = Field(..., description="源类型，可选值为 'local' 或 'remote'")

    @classmethod
    def generate_random_params(cls):
        return cls(
            repo_url=f"https://github.com/test/repo_{uuid4()}.git",
            username=f"test_user_{uuid4()}",
            branch="main",
            source="remote"
        )


class CreateProjectResponse(BaseModel):
    """创建项目响应"""
    project_id: str = Field(..., description="新创建项目的UUID")
    is_new: bool = Field(
        ..., description="如果为True，表示项目是新创建的；否则表示是获取的已存在项目。"
    )
    

class MerkleTreeNode(BaseModel):
    """Merkle树节点"""
    path: str = Field(..., description="文件或目录的相对路径")
    is_file: bool = Field(..., description="如果为true，则为文件节点；否则为目录节点")
    hash: str = Field(..., description="文件内容的哈希值，目录节点为其子节点哈希的组合")
    children: Optional[List['MerkleTreeNode']] = Field(default_factory=list, description="子节点列表，仅目录节点有效")


class CompareProjectRequest(BaseModel):
    """比较项目版本请求"""
    merkle_tree: MerkleTreeNode = Field(..., description="客户端生成的项目文件Merkle树")


class DiffFiles(BaseModel):
    """文件差异"""
    added: List[str] = Field(default_factory=list, description="新增文件的路径列表")
    modified: List[str] = Field(default_factory=list, description="修改文件的路径列表")
    deleted: List[str] = Field(default_factory=list, description="删除文件的路径列表")


class CompareProjectResponse(BaseModel):
    """比较项目版本响应"""
    project_id: str = Field(..., description="项目的UUID")
    server_version: int = Field(..., description="服务端当前的项目版本时间戳")
    diff_files: DiffFiles = Field(..., description="需要上传的文件差异列表")
    task_id: str = Field(..., description="为本次文件同步创建的新任务ID")


# ============================================================================
# 代码库任务相关模型
# ============================================================================

class FileUpload(BaseModel):
    """上传文件信息"""
    file_path: str = Field(..., description="文件的相对路径")
    file_content: str = Field(..., description="文件的内容")
    file_hash: str = Field(..., description="文件的base64编码")

class UploadFilesRequest(BaseModel):
    """上传文件请求"""
    files: List[FileUpload] = Field(..., description="要上传的文件列表")
    auto_commit: Optional[bool] = Field(default=True, description="如果为true，则在上传后自动提交任务进行处理")


class FileUploadResult(BaseModel):
    """文件上传结果"""
    file_path: str = Field(..., description="已上传文件的路径")
    status: str = Field(..., description="上传状态，可能的值包括 'pending', 'processing', 'completed', 'failed'")
    message: str = Field(..., description="关于上传状态的可选消息")


class UploadFilesResponse(BaseModel):
    """上传文件响应"""
    task_id: str = Field(..., description="任务的UUID")
    results: List[FileUploadResult] = Field(..., description="每个文件上传的结果")
    overall_status: str = Field(..., description="批量上传的总体状态消息")
    task_status: str = Field(..., description="上传后任务的当前状态")


class FilesStatus(BaseModel):
    """文件处理状态"""
    pending: List[str] = Field(default_factory=list, description="待处理的文件列表")
    parsing: List[str] = Field(default_factory=list, description="正在解析的文件列表")
    vectorizing: List[str] = Field(default_factory=list, description="正在向量化的文件列表")
    completed: List[str] = Field(default_factory=list, description="已完成处理的文件列表")
    failed: List[str] = Field(default_factory=list, description="处理失败的文件列表")


class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    task_id: str = Field(..., description="任务的UUID")
    status: str = Field(..., description="任务的当前状态，可能的值包括 'pending_upload', 'received', 'processing', 'success', 'failed', 'rolled_back'")
    files_status: Optional[FilesStatus] = Field(default=None, description="各文件的处理状态")
    error_message: Optional[str] = Field(default=None, description="如果任务失败，则包含错误消息")
    updated_at: Optional[str] = Field(default=None, description="状态最后更新的时间戳")


# ============================================================================
# 检索与问答相关模型
# ============================================================================

class RetrievalOptions(BaseModel):
    """检索选项"""
    stream: Optional[bool] = Field(default=False, description="是否流式返回结果")
    max_context_chunks: Optional[int] = Field(default=10, description="最大上下文块数")


class CodeRecallRequest(BaseModel):
    """代码召回请求"""
    project_id: str = Field(..., description="项目ID")
    repo_url: str = Field(..., description="代码仓库的URL")
    branch: str = Field(..., description="分支名称")
    username: str = Field(..., description="用户标识符")
    query: str = Field(..., description="自然语言查询")
    retrieval_type: Optional[str] = Field(default="text", description="检索类型，可选值为 'text' (语义), 'associate' (上下文), 'chunk' (代码召回)")
    options: Optional[RetrievalOptions] = Field(default_factory=RetrievalOptions, description="附加选项")


class CodeSource(BaseModel):
    """代码源信息"""
    file_path: str = Field(..., description="文件路径")
    # location: List[int] = Field(..., description="代码在文件中的位置 [开始行, 结束行]")
    relevance_score: float = Field(..., description="相关性得分，范围从0到1")


class CodeRecallResponse(BaseModel):
    """代码召回响应"""
    documents: List[Dict[str, Any]] = Field(default_factory=list, description="检索到的文档列表")

    # 为了向后兼容，添加属性来模拟原有的answer和sources字段
    @property
    def answer(self) -> str:
        """生成基于文档的回答"""
        if not self.documents:
            return "No relevant code found."
        return f"Found {len(self.documents)} relevant code documents."

    @property
    def sources(self) -> List[CodeSource]:
        """将documents转换为CodeSource格式"""
        sources = []
        for doc in self.documents:
            if isinstance(doc, dict):
                sources.append(CodeSource(
                    file_path=doc.get('metadata', {}).get('file_path', 'unknown'),
                    relevance_score=doc.get('score', 0.0)
                ))
        return sources


# ============================================================================
# 通用模型
# ============================================================================

class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")


# 更新前向引用
MerkleTreeNode.model_rebuild()
