import json
from typing import List, Dict
from pydantic import BaseModel, Field, ConfigDict

from services.service import IService
from utils.https import HTTPClient, HTTPError
from utils.logger import logger


class SearchResponse(BaseModel):
    query: str
    code_snippets: str

class CodeBaseDevApi(IService):
    def __init__(self, config):
        super().__init__(config)

        self.http_client = HTTPClient(base_url=config['api_url'], timeout=config['timeout'])
        logger.info(f"CodeBaseDevApi initialized with base_url: {config['api_url']}")

    def load_context_files(self):
        pass

    def retrieve(self, query: str, workspaceName: str, search_type: str = "term_sparse", stream: bool = False) -> Dict[str, float]:
        try:
            response = self.http_client.post("/search", data={
                "query": query,
                "workspace_name": workspaceName,
                "search_type": search_type,
                "is_stream": stream
            })
                
            response_data = SearchResponse(**response)

            code_snippets = json.loads(response_data.code_snippets)
            
            return {snippet['file_path']: snippet['score'] for snippet in code_snippets}
            
        except KeyError as e:
            logger.error(f"Missing required field in response:", e)
            return {}
        except Exception as e:
            logger.error(f"Error in retrieve method:", e)
            return {}
    