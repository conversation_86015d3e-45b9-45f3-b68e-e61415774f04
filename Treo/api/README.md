# CodeBase API 客户端

这个模块提供了与代码理解RAG服务的完整API接口，包括项目管理、文件上传、任务状态查询和代码检索功能。

## 目录结构

```
api/
├── README.md           # 本文档
├── api_reference.md    # API接口文档
├── codebase.py        # 主要的API客户端类
└── schema.py          # Pydantic数据模型定义
```

## 快速开始

### 1. 安装依赖

```bash
# 项目已包含所需依赖
pip install pydantic requests rich
```

### 2. 基本使用

```python
from api.codebase import CodeBaseApi

# 初始化API客户端
api = CodeBaseApi(base_url="http://localhost:8000")

# 可选：设置认证token
api.set_auth_token("your-auth-token")

# 创建项目
project_id = api.create_project(
    repo_url="https://github.com/your/repo.git",
    username="your_username",
    branch="main",
    source="remote"
)

# 代码检索
results = api.search(
    repo_url="https://github.com/your/repo.git",
    query="如何实现用户认证？",
    username="your_username",
    top_k=5
)

# 关闭客户端
api.close()
```

### 3. 使用上下文管理器

```python
with CodeBaseApi(base_url="http://localhost:8000") as api:
    # 自动处理资源清理
    results = api.search(
        repo_url="https://github.com/your/repo.git",
        query="authentication implementation",
        username="user"
    )
```

## 主要功能

### 项目管理

#### 创建项目
```python
project_id = api.create_project(
    repo_url="https://github.com/example/repo.git",
    username="developer",
    branch="main",
    source="remote"  # 或 "local"
)
```

#### 比较项目版本
```python
from api.schema import MerkleTreeNode

# 构建Merkle树
merkle_tree = MerkleTreeNode(
    path="project_root",
    is_file=False,
    hash="root_hash",
    children=[
        MerkleTreeNode(
            path="src/main.py",
            is_file=True,
            hash="file_hash_1"
        )
    ]
)

# 比较版本
compare_result = api.compare_project_version(project_id, merkle_tree)
print(f"Task ID: {compare_result.task_id}")
print(f"Added files: {compare_result.diff_files.added}")
```

### 文件上传

#### 上传文件内容
```python
from api.schema import FileUpload

files = [
    FileUpload(
        file_path="src/main.py",
        file_content="print('Hello, World!')"
    ),
    FileUpload(
        file_path="src/utils.py",
        file_content="def helper():\n    pass"
    )
]

upload_result = api.upload_files(task_id, files, auto_commit=True)
```

#### 从文件路径上传
```python
# 便捷方法：直接从文件路径上传
file_paths = ["src/main.py", "src/utils.py", "README.md"]
upload_result = api.upload_files_from_paths(task_id, file_paths)
```

### 任务状态管理

#### 查询任务状态
```python
status = api.get_task_status(task_id)
print(f"Status: {status.status}")
print(f"Completed files: {status.files_status.completed}")
```

#### 等待任务完成
```python
try:
    final_status = api.wait_for_task_completion(
        task_id, 
        max_wait_time=300,  # 最大等待5分钟
        check_interval=5    # 每5秒检查一次
    )
    print(f"Task completed with status: {final_status.status}")
except TimeoutError:
    print("Task did not complete within the specified time")
```

### 代码检索

#### 高级代码召回
```python
recall_result = api.code_recall(
    repo_url="https://github.com/example/repo.git",
    branch="main",
    username="developer",
    query="如何处理用户认证？",
    retrieval_type="text",  # "text", "associate", "chunk"
    max_context_chunks=10,
    stream=False
)

print(f"Answer: {recall_result.answer}")
for source in recall_result.sources:
    print(f"File: {source.file_path}")
    print(f"Location: {source.location}")
    print(f"Score: {source.relevance_score}")
```

#### 简化搜索接口
```python
# 兼容原有接口的简化搜索
results = api.search(
    repo_url="https://github.com/example/repo.git",
    query="authentication function",
    username="developer",
    branch="main",
    top_k=5
)

for result in results:
    print(f"File: {result['file_path']}")
    print(f"Score: {result['score']}")
```

## 错误处理

```python
from utils.https import HTTPError

try:
    project_id = api.create_project(
        repo_url="invalid-url",
        username="user",
        branch="main"
    )
except HTTPError as e:
    print(f"HTTP Error {e.status_code}: {e}")
    if e.response_data:
        print(f"Error details: {e.response_data}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## 配置选项

### 初始化参数
```python
api = CodeBaseApi(
    base_url="http://localhost:8000",  # API服务器地址
    timeout=30                         # 请求超时时间（秒）
)
```

### 设置请求头
```python
api.set_header("X-Custom-Header", "value")
api.set_auth_token("your-jwt-token")
```

## 日志记录

API客户端使用项目的统一日志系统，所有请求和响应都会被记录：

```python
from utils.logging import logger

# 日志会自动记录到控制台和文件
# 可以通过logger调整日志级别
logger.setLevel(logging.DEBUG)  # 显示详细的请求/响应信息
```

## 数据模型

所有的请求和响应数据都使用Pydantic模型进行验证，确保数据类型安全：

```python
from api.schema import (
    CreateProjectRequest, CreateProjectResponse,
    MerkleTreeNode, FileUpload,
    CodeRecallRequest, CodeRecallResponse
)

# 模型支持JSON序列化
request_data = CreateProjectRequest(
    repo_url="https://github.com/example/repo.git",
    username="user",
    branch="main",
    source="remote"
)

json_data = request_data.model_dump()  # 转换为字典
json_str = request_data.model_dump_json()  # 转换为JSON字符串
```

## 测试

运行测试脚本验证API客户端功能：

```bash
python test_api.py
```

注意：完整的API测试需要运行实际的API服务器。
