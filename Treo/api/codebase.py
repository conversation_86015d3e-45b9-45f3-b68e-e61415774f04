
"""
代码库API客户端
提供与代码理解RAG服务的完整API接口
"""

from typing import List, Dict
from utils.https import HTTPClient, HTTPError
from utils.logger import logger
from api.schema import (
    CreateProjectRequest, CreateProjectResponse,
    CompareProjectRequest, CompareProjectResponse,
    UploadFilesRequest, UploadFilesResponse,
    TaskStatusResponse,
    CodeRecallRequest, CodeRecallResponse,
    MerkleTreeNode, FileUpload
)
from services.service import IService

class CodeBaseApi(IService):
    """代码库API客户端"""

    def __init__(self, config: Dict):
        """
        初始化API客户端

        Args:
            base_url: API服务器基础URL
            timeout: 请求超时时间（秒）
        """
        super().__init__(config)

        self.http_client = HTTPClient(base_url=config['api_url'], timeout=config['timeout'])
        logger.info(f"CodeBaseApi initialized with base_url: {config['api_url']}")

    def set_auth_token(self, token: str):
        """设置认证token"""
        self.http_client.set_auth_token(token)
        logger.info("Authentication token set")

    # ========================================================================
    # 项目管理接口
    # ========================================================================

    def create_project(self, repo_url: str, username: str, branch: str = "master", source: str = "local") -> CreateProjectResponse:
        """
        创建项目

        Args:
            repo_url: 代码仓库的URL
            username: 用户标识符
            branch: 分支名称
            source: 源类型，可选值为 "local" 或 "remote"

        Returns:
            项目ID

        Raises:
            HTTPError: 请求失败时抛出
        """
        logger.info(f"⏱️  [API START] create_project - repo_url: {repo_url}, username: {username}")

        request_data = CreateProjectRequest(
            repo_url=repo_url,
            username=username,
            branch=branch,
            source=source
        )

        response_data = self.http_client.post("/api/v1/project", data=request_data.model_dump())
        return CreateProjectResponse(**response_data)

    def load_context_files(self, project_id: str, merkle_tree: MerkleTreeNode, upload_files: List[FileUpload]):
        """
        比较项目版本

        Args:
            project_id: 项目ID
            merkle_tree: 客户端生成的项目文件Merkle树

        Returns:
            比较结果，包含差异文件和任务ID

        Raises:
            HTTPError: 请求失败时抛出
        """
        logger.info(f"⏱️  [API START] compare_project_version - project_id: {project_id}")

        request_data = CompareProjectRequest(merkle_tree=merkle_tree)

        response_data = self.http_client.post(
            f"/api/v1/project/{project_id}/compare",
            data=request_data.model_dump()
        )
        response = CompareProjectResponse(**response_data)

        logger.info(f"Diff files - Added: {len(response.diff_files.added)}, "
                    f"Modified: {len(response.diff_files.modified)}, "
                    f"Deleted: {len(response.diff_files.deleted)}")

        # 上传代码内容
        self.upload_files(response.task_id, upload_files)

    # ========================================================================
    # 代码库任务接口
    # ========================================================================

    def upload_files(self, task_id: str, files: List[FileUpload], auto_commit: bool = True):
        """
        上传文件

        Args:
            task_id: 任务ID
            files: 要上传的文件列表
            auto_commit: 是否自动提交任务进行处理

        Returns:
            上传结果

        Raises:
            HTTPError: 请求失败时抛出
        """
        logger.info(f"⏱️  [API START] upload_files - task: {task_id}, files: {len(files)}")

        # 批量发送请求，每次最多1000条files
        chunk_size = 1000   
        for i in range(0, len(files), chunk_size):
            chunk = files[i:i+chunk_size]
            request_data = UploadFilesRequest(files=chunk, auto_commit=auto_commit)
            response_data = self.http_client.post(
                f"/api/v1/codebase-task/{task_id}/upload",
                data=request_data.model_dump()
            )
            response = UploadFilesResponse(**response_data)
            logger.info(f"Upload {len(chunk)} files response: {response}")

        # 检查阻塞上传进度
        task_status_response: TaskStatusResponse =  self.wait_for_task_completion(task_id)

        if task_status_response.status != 'success':
            raise RuntimeError(f"Failed to upload files for task {task_id}")    
        
    def get_task_status(self, task_id: str) -> TaskStatusResponse:
        """
        查询任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息

        Raises:
            HTTPError: 请求失败时抛出
        """
        logger.info(f"⏱️  [API START] get_task_status - task: {task_id}")

        response_data = self.http_client.get(f"/api/v1/codebase-task/{task_id}/status")
        response = TaskStatusResponse(**response_data)
        return response
    
    # ========================================================================
    # 检索与问答接口
    # ========================================================================

    def retrieve(self, project_id: str, repo_url: str, branch: str, username: str, query: str,
                   retrieval_type: str = "chunk", max_context_chunks: int = 10,
                   stream: bool = False) -> Dict[str, float]:
        """
        代码召回

        Args:
            project_id: 项目ID
            repo_url: 代码仓库的URL
            branch: 分支名称
            username: 用户标识符
            query: 自然语言查询
            retrieval_type: 检索类型，可选值为 "text" (语义), "associate" (上下文), "chunk" (代码召回)
            max_context_chunks: 最大上下文块数
            stream: 是否流式返回结果

        Returns:
            代码召回结果

        Raises:
            HTTPError: 请求失败时抛出
        """
        logger.info(f"⏱️  [API START] code_recall - project: {project_id}, query: {query[:50]}...")
        # 接口限制
        query = query[:1000]
        
        from api.schema import RetrievalOptions

        request_data = CodeRecallRequest(
            project_id=project_id,
            repo_url=repo_url,
            branch=branch,
            username=username,
            query=query,
            retrieval_type=retrieval_type,
            options=RetrievalOptions(stream=stream, max_context_chunks=max_context_chunks)
        )
        response_data = self.http_client.post("/api/v1/retrieval/recall", data=request_data.model_dump())
        response = CodeRecallResponse(**response_data)
        
        recall_docs = {}
        for source in response.sources:
            recall_docs[source.file_path] = source.relevance_score

        return recall_docs


    def wait_for_task_completion(self, task_id: str, max_wait_time: int = 200000,
                                check_interval: int = 5) -> TaskStatusResponse:
        """
        等待任务完成

        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒）
            check_interval: 检查间隔（秒）

        Returns:
            最终任务状态

        Raises:
            TimeoutError: 超时时抛出
            HTTPError: 请求失败时抛出
        """
        import time

        start_time = time.time()
        logger.info(f"Waiting for task {task_id} to complete (max {max_wait_time}s)")

        while time.time() - start_time < max_wait_time:
            status = self.get_task_status(task_id)

            if status.status in ["success", "failed", "rolled_back"]:
                logger.info(f"Task {task_id} completed with status: {status.status}")
                return status

            logger.debug(f"Task {task_id} status: {status.status}, waiting...")
            time.sleep(check_interval)

        raise TimeoutError(f"Task {task_id} did not complete within {max_wait_time} seconds")

    def close(self):
        """关闭HTTP客户端"""
        self.http_client.close()
        logger.info("CodeBaseApi client closed")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
        return False
