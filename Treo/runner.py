
import os
import time
from api.codebase import Code<PERSON><PERSON><PERSON><PERSON>
from api.codebase_dev import CodeBaseDevApi
from services.lexcial.lexcial_bm25 import LexicalBM25
from services.lexcial.lexcial_tfidf import LexicalTFIDF

from benchmarks.treo.treo_bm25_lexcial_benchmark import TreoBM25LexicalBenchmarkModel
from benchmarks.treo.treo_tfidf_lexcial_benchmark import TreoTFIDFLexicalBenchmarkModel
from benchmarks.treo.treo_joycoder_benchmark import TreoJoycoderBenchmarkModel
from benchmarks.treo.treo_codebase_benchmark import TreoCodebaseBenchmarkModel
from benchmarks.coir.coir_bm25_lexcial_benchmark import CoirBM25LexicalBenchmarkModel
from benchmarks.coir.coir_tfidf_lexcial_benchmark import CoirTFIDFLexicalBenchmarkModel
from benchmarks.coir.coir_joycoder_benchmark import CoirJoycoderBenchmarkModel

if __name__ == "__main__":
    # bm25_service = LexicalBM25()
    # treo_bm25_benchmark_model = TreoBM25LexicalBenchmarkModel(bm25_service, 
    #                                                           qas_file="../treo/data/qas.json", 
    #                                                           output_dir="./results/treo/bm25",
    #                                                           cache_dir="../treo/cache/treo/bm25")
    # treo_bm25_benchmark_model.init_dataset()
    # treo_bm25_benchmark_model.evaluate()

    # bm25_service = LexicalBM25()
    # coir_bm25_benchmark_model = CoirBM25LexicalBenchmarkModel(bm25_service, 
    #                                                           output_dir="./results/coir/bm25", 
    #                                                           cache_dir="../treo/cache/coir/bm25",
    #                                                           tasks=["stackoverflow-qa","codefeedback-mt","codefeedback-st","cosqa","CodeSearchNet-go", "CodeSearchNet-java", "CodeSearchNet-javascript", "CodeSearchNet-python"])
    # coir_bm25_benchmark_model.init_dataset()
    # coir_bm25_benchmark_model.evaluate(top_k=10)
    
    # tfidf_service = LexicalTFIDF()
    # treo_bm25_benchmark_model = TreoTFIDFLexicalBenchmarkModel(tfidf_service, 
    #                                                            qas_file="../treo/data/qas.json", 
    #                                                            cache_dir="../treo/cache/treo/tfidf",
    #                                                            output_dir="./results/treo/tfidf")
    # treo_bm25_benchmark_model.init_dataset()
    # treo_bm25_benchmark_model.evaluate()

    # tfidf_service = LexicalTFIDF()
    # coir_tfidf_benchmark_model = CoirTFIDFLexicalBenchmarkModel(tfidf_service, 
    #                                                            output_dir="./results/coir/tfidf", 
    #                                                            cache_dir="../treo/cache/coir/tfidf",
    #                                                            tasks=["stackoverflow-qa","codefeedback-mt","codefeedback-st","cosqa","CodeSearchNet-go", "CodeSearchNet-java", "CodeSearchNet-javascript", "CodeSearchNet-python"])
    # coir_tfidf_benchmark_model.init_dataset()
    # coir_tfidf_benchmark_model.evaluate()

    output_dir = "./results/treo/codebase_dev"
    time_str = time.strftime("%Y%m%d%H%M%S", time.localtime())
    output_dir = os.path.join(output_dir, time_str)
    
    # for question_key in ["question", "prev_msg"]:
    #     base_api = CodeBaseApi({"api_url": "http://localhost:8080", "timeout": 300})
    #     treo_joycoder_benchmark_model = TreoJoycoderBenchmarkModel(
    #         base_api, 
    #         qas_file="/Users/<USER>/01-Projects/JoyCodeEvaluate/JoyAutoTask/grep_eval/qas_eval.json", 
    #         output_dir=output_dir
    #     )
    #     treo_joycoder_benchmark_model.init_dataset()
    #     treo_joycoder_benchmark_model.evaluate(question_key=question_key)

    for question_key in ["question"]:
        base_api = CodeBaseDevApi({"api_url": "http://localhost:3451/api/v1", "timeout": 300})
        treo_codebase_benchmark_model = TreoCodebaseBenchmarkModel(
            base_api, 
            qas_file="/Users/<USER>/01-Projects/treo/verified/qas_lite.json", 
            output_dir=output_dir
        )
        treo_codebase_benchmark_model.init_dataset()
        treo_codebase_benchmark_model.evaluate(question_key=question_key, max_workers=6, search_tool="inverted_index")

    
    # base_api = CodeBaseApi({"api_url": "http://localhost:8080", "timeout": 200000})
    # coir_joycoder_benchmark_model = CoirJoycoderBenchmarkModel(
    #     base_api,
    #     output_dir="./results/coir/joycoder",
    #     tasks=["stackoverflow-qa","codefeedback-mt","codefeedback-st","cosqa","CodeSearchNet-go", "CodeSearchNet-java", "CodeSearchNet-javascript", "CodeSearchNet-python"],
    #     concurrent_workers=8  # 设置并发数为8
    # )
    # coir_joycoder_benchmark_model.init_dataset()
    # coir_joycoder_benchmark_model.evaluate()

    # with open("./benchmarks/treo/data/qas.json", "r", encoding="utf-8") as f:
    #     qas_data = json.load(f)
    #     for repo_data in rich.progress.track(qas_data, description="Translating..."):
    #         repo_local_path = repo_data['local_path']
    #         qa_list = repo_data['qa']
    #         for qa_item in qa_list:
    #             answer = qa_item['answer']
    #             answer_en = translate_chinese_to_english(answer)
    #             qa_item['answer_en'] = answer_en
    #     with open("./benchmarks/treo/data/qas_en.json", "w", encoding="utf-8") as f:
    #         json.dump(qas_data, f, ensure_ascii=False, indent=2)
